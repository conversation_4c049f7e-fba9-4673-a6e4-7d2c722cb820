# ninja log v5
57186	63568	7701141564197643	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o	2b989b0a0c0a0f2
1	76	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/arm64-v8a/CMakeFiles/cmake.verify_globs	6fa82bcdac9d6fe5
191	8934	7701141016235117	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	df6f1f35293c7e3d
9125	15721	7701141084611598	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o	e457459f5a6d64fa
8788	16118	7701141089645883	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	9121a34fb5122a3f
27158	33752	7701141266139877	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o	c4d406d301496a71
9064	17364	7701141101409629	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	fbe5aea47d39533
114	8906	7701362239391777	CMakeFiles/appmodules.dir/OnLoad.cpp.o	bf5adfb89131bf95
69116	76170	7701141690910739	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	334151c039725254
65728	72037	7701141649213498	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	753d5a2088adc882
6931	19030	7701141118219054	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	34da603069393945
93	1266	7701464234582860	build.ninja	cb463c1733966546
47528	48902	7701141415262162	E:/HYDRA-IOT/HydraApp/android/app/build/intermediates/cxx/Debug/1n1k36bs/obj/arm64-v8a/libreact_codegen_safeareacontext.so	858bebf12f658c4b
10329	19421	7701141122468127	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o	eb0b707e7e3fb01f
35516	40008	7701141329129764	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/550838433058dc1fd573b81ffaad7ac7/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o	ad311850afd72907
8804	20794	7701141135190125	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	efbecb3ef3a9d87b
61408	67173	7701141599408383	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a94ba4a7694248275b58ea6e1b34e234/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o	c5b4fda6791d97c0
49620	58429	7701141513024733	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ad0053095b3309824e38725beb230ec4/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	f32b877b6bf9e7b3
68793	75298	7701141681869267	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	d75528377a2ef82d
25956	34208	7701141271129465	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o	7a5b8bdf2f8c8852
43086	51035	7701141439072456	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3d8ed2bade843d889bd16dd10152ddbf/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	5183faf352554da6
26306	32892	7701141257476179	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o	904ab5deb7f3da8
15722	26306	7701141191229131	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	f790f85795ea51ed
65531	74845	7701141676524716	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a75f7d151350c2ad12e3fcc23620b108/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o	8663266e319420c0
40008	47527	7701141404264125	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/afeb5cd97c1b24e6ae06486f4d02acf9/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o	88828bcefc886606
32000	36344	7701141292146302	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o	7106d40b35d7c7b2
27783	35515	7701141284150496	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o	51b4a1f78db7187
61797	67531	7701141604266872	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a75f7d151350c2ad12e3fcc23620b108/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o	8e3816822f559a5d
26872	33759	7701141265524735	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o	b8a07ed6b7d3faf3
28913	35787	7701141286776379	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o	6b9f036fe99d9292
33760	41128	7701141340584264	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9c69b1f7d8ca70e3a7cc148200b22236/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o	d3be0244dc4576cc
34209	43086	7701141359749763	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/550838433058dc1fd573b81ffaad7ac7/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o	331c653c8c8b92
123	25589	7701362404599186	CMakeFiles/appmodules.dir/E_/HYDRA-IOT/HydraApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	e43eff840693cc34
35789	43100	7701141359829754	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/891b7855b2d125d04ac7afa152cad07e/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o	18500d357a19fbfa
32892	39184	7701141320792814	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9c69b1f7d8ca70e3a7cc148200b22236/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o	cdd47b49d54ede10
67174	75210	7701141679918625	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	1d534867e8662982
33752	45099	7701141379831614	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b7046d699b6e0fe79e4beb14c8a51d24/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o	efac5bf134615fe9
25590	27634	7701362426221472	E:/HYDRA-IOT/HydraApp/android/app/build/intermediates/cxx/Debug/1n1k36bs/obj/arm64-v8a/libappmodules.so	5b68317064c54f93
42779	49620	7701141424697481	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	1b1385ae6e57501e
32017	40648	7701141335456567	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f17cb6db24b6c3165dd89b8b5f73a508/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	9b7de4f46352fba6
36345	42778	7701141356723903	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/25e3f1442121f0e330c9ba177608309a/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o	5f8eff9a23d9804e
43101	49762	7701141426457981	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	37fb9cbe8359a4b
40649	48117	7701141410203206	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	e129c339949656e5
58429	68792	7701141616690533	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a94ba4a7694248275b58ea6e1b34e234/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o	75bf7083168c8511
45099	53140	7701141459692978	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3d8ed2bade843d889bd16dd10152ddbf/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	4225861e8cc58f5d
70708	76814	7701141697440670	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o	dc72e4ce0dfea752
17365	24352	7701141172377600	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/EventEmitters.cpp.o	9c6aac06f61752ad
72042	76696	7701141696315574	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	d0265a5b2012c3e2
48118	57186	7701141500576906	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	5ec1db9244429d2e
53140	61797	7701141546511439	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o	91f1b1c43ed3289d
62669	63773	7701141563542320	E:/HYDRA-IOT/HydraApp/android/app/build/intermediates/cxx/Debug/1n1k36bs/obj/arm64-v8a/libreact_codegen_rnscreens.so	fe2422d21b55b7cd
39185	46941	7701141398426345	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	5fb218e17519d32c
48902	62669	7701141554770701	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/598dc9a4de586092988288a9ed8b2a9c/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	fe9080e2647a5b09
41128	49920	7701141426557926	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	8e6ba56a92b13392
46942	52997	7701141458277618	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	1fc6fcdc79025f64
49763	59062	7701141519136039	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/50238a92379787fd4f2f91cf6600c217/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	4343ffa1d982e0ee
52997	58259	7701141510924117	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/50238a92379787fd4f2f91cf6600c217/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	afb6eae7bff6d59e
49921	56457	7701141493514397	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/598dc9a4de586092988288a9ed8b2a9c/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	adcb4675b08b0cf9
51036	61408	7701141541734744	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ad0053095b3309824e38725beb230ec4/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	ef57f8959dd1b99d
58260	65727	7701141586236650	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o	99f37c2849c5643f
67533	75341	7701141681463889	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	da33cc5494ac6a80
56457	65531	7701141584346112	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o	a413e0684dc09642
59062	69067	7701141618230944	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o	a19f186e06e0e5b8
63773	70707	7701141635786207	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a94ba4a7694248275b58ea6e1b34e234/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o	8ae94efedc7ab0d5
63604	75330	7701141680468637	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a75f7d151350c2ad12e3fcc23620b108/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o	a86eac54c2dd0550
75330	75717	7701141685354135	E:/HYDRA-IOT/HydraApp/android/app/build/intermediates/cxx/Debug/1n1k36bs/obj/arm64-v8a/libreact_codegen_rnsvg.so	5455b68a9b81e6fa
24353	32017	7701141248729405	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/Props.cpp.o	42d3442d836b401f
19421	28913	7701141218132308	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/ShadowNodes.cpp.o	9d9b24e5b25d4f65
15764	26872	7701141197756225	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/ComponentDescriptors.cpp.o	884959c4d85841a9
19030	27158	7701141200518615	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/RNImagePickerSpecJSI-generated.cpp.o	db9c433a185d0c48
16119	25956	7701141188413121	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/RNImagePickerSpec-generated.cpp.o	322d66930f04007b
20930	27782	7701141206264953	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/States.cpp.o	a3ae896a68993c84
1	22	0	clean	1ffa0f1d946a52ac
268	6930	7701140996887398	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	163a787c4253f9be
186	7183	7701141000682854	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	59bed30267de70d7
263	9118	7701141019418828	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	7932ccf1d03d8fdd
188	8803	7701141016738025	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	16304a144758aa26
7254	15764	7701141086216944	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	7b43fb4ebeaee057
248	8785	7701141016135170	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	b51b519c18c68324
2	82	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/arm64-v8a/CMakeFiles/cmake.verify_globs	6fa82bcdac9d6fe5
1	436	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/arm64-v8a/CMakeFiles/cmake.verify_globs	6fa82bcdac9d6fe5
1	292	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/arm64-v8a/CMakeFiles/cmake.verify_globs	6fa82bcdac9d6fe5
1	83	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/arm64-v8a/CMakeFiles/cmake.verify_globs	6fa82bcdac9d6fe5
1	73	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/arm64-v8a/CMakeFiles/cmake.verify_globs	6fa82bcdac9d6fe5
26	109	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/arm64-v8a/CMakeFiles/cmake.verify_globs	6fa82bcdac9d6fe5
2	31	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/arm64-v8a/CMakeFiles/cmake.verify_globs	6fa82bcdac9d6fe5
0	58	0	clean	1ffa0f1d946a52ac

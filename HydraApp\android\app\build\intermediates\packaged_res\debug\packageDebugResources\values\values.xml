<?xml version="1.0" encoding="utf-8"?>
<resources>
    <integer name="react_native_dev_server_port">8081</integer>
    <string name="app_name">HydraApp</string>
    <string name="gcm_defaultSenderId" translatable="false">225572897754</string>
    <string name="google_api_key" translatable="false">AIzaSyCO23I1l-JguWoxinmyWsNyeVChGfcT5B4</string>
    <string name="google_app_id" translatable="false">1:225572897754:android:eff0db3de6bb0f2b70b6e3</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyCO23I1l-JguWoxinmyWsNyeVChGfcT5B4</string>
    <string name="google_storage_bucket" translatable="false">hydra-iot-f5fda.firebasestorage.app</string>
    <string name="project_id" translatable="false">hydra-iot-f5fda</string>
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    </style>
</resources>
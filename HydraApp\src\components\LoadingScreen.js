import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  StatusBar,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, SIZES, FONTS } from '../constants/theme';

const LoadingScreen = ({ message = 'Loading...' }) => {
  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />
      <LinearGradient
        colors={COLORS.gradientPrimary}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      >
        <View style={styles.content}>
          {/* App Logo/Icon */}
          <View style={styles.logoContainer}>
            <Icon name="sprout" size={80} color={COLORS.white} />
            <Text style={styles.appName}>HYDRA</Text>
            <Text style={styles.appSubtitle}>IoT Plant Watering System</Text>
          </View>

          {/* Loading Indicator */}
          <View style={styles.loadingContainer}>
            <ActivityIndicator 
              size="large" 
              color={COLORS.white} 
              style={styles.spinner}
            />
            <Text style={styles.loadingText}>{message}</Text>
          </View>

          {/* Animated Dots */}
          <View style={styles.dotsContainer}>
            <View style={[styles.dot, styles.dot1]} />
            <View style={[styles.dot, styles.dot2]} />
            <View style={[styles.dot, styles.dot3]} />
          </View>
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: SIZES.paddingLarge,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: SIZES.marginXL * 2,
  },
  appName: {
    fontSize: FONTS.h1,
    fontWeight: '800',
    color: COLORS.white,
    marginTop: SIZES.marginMedium,
    letterSpacing: 2,
  },
  appSubtitle: {
    fontSize: FONTS.body2,
    color: COLORS.white,
    opacity: 0.9,
    marginTop: SIZES.marginXS,
    textAlign: 'center',
  },
  loadingContainer: {
    alignItems: 'center',
    marginBottom: SIZES.marginXL,
  },
  spinner: {
    marginBottom: SIZES.marginMedium,
  },
  loadingText: {
    fontSize: FONTS.body1,
    color: COLORS.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: COLORS.white,
    marginHorizontal: 4,
    opacity: 0.6,
  },
  dot1: {
    // Animation can be added here if needed
  },
  dot2: {
    // Animation can be added here if needed
  },
  dot3: {
    // Animation can be added here if needed
  },
});

export default LoadingScreen;

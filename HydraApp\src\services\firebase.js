import { initializeApp, getApps } from 'firebase/app';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getDatabase } from 'firebase/database';
import { getFirestore } from 'firebase/firestore';
import {
  FIREBASE_API_KEY,
  FIREBASE_AUTH_DOMAIN,
  FIREBASE_DATABASE_URL,
  FIREBASE_PROJECT_ID,
  FIREBASE_STORAGE_BUCKET,
  FIREBASE_MESSAGING_SENDER_ID,
  FIREBASE_APP_ID
} from '@env';

// Firebase configuration using environment variables
const firebaseConfig = {
  apiKey: FIREBASE_API_KEY,
  authDomain: FIREBASE_AUTH_DOMAIN,
  databaseURL: FIREBASE_DATABASE_URL,
  projectId: FIREBASE_PROJECT_ID,
  storageBucket: FIREBASE_STORAGE_BUCKET,
  messagingSenderId: FIREBASE_MESSAGING_SENDER_ID,
  appId: FIREBASE_APP_ID
};

// Validate essential config
if (!firebaseConfig.apiKey || !firebaseConfig.projectId || !firebaseConfig.appId) {
  throw new Error('[HYDRA-IOT] CRITICAL: Firebase configuration is missing essential fields (apiKey, projectId, or appId).');
}

// Initialize Firebase app (check if already initialized)
let app;
if (getApps().length === 0) {
  console.log('[HYDRA-IOT] Initializing Firebase app...');
  app = initializeApp(firebaseConfig);
  console.log('[HYDRA-IOT] Firebase app initialized successfully');
} else {
  console.log('[HYDRA-IOT] Firebase app already initialized, using existing instance');
  app = getApps()[0];
}

// Initialize Firebase Auth with lazy loading to avoid registration issues
let auth;

const initializeFirebaseAuth = async () => {
  try {
    console.log('[HYDRA-IOT] Initializing Firebase Auth...');

    // Dynamically import auth functions to avoid early registration issues
    const { getReactNativePersistence, initializeAuth, getAuth } = await import('firebase/auth');

    // For React Native, we need to use initializeAuth with persistence
    // Only initialize if not already initialized
    const existingApps = getApps();
    const existingApp = existingApps.find(app => app.name === '[DEFAULT]');

    if (existingApp) {
      try {
        // Try to get existing auth instance
        auth = getAuth(existingApp);
        console.log('[HYDRA-IOT] Using existing Firebase Auth instance');
      } catch (getAuthError) {
        // If getAuth fails, the auth component might not be initialized yet
        console.log('[HYDRA-IOT] Auth not initialized, creating new instance...');
        auth = initializeAuth(existingApp, {
          persistence: getReactNativePersistence(AsyncStorage),
        });
        console.log('[HYDRA-IOT] Firebase Auth initialized with persistence');
      }
    } else {
      // Initialize auth for the current app
      auth = initializeAuth(app, {
        persistence: getReactNativePersistence(AsyncStorage),
      });
      console.log('[HYDRA-IOT] Firebase Auth initialized for new app');
    }

    // Verify auth is properly initialized
    if (!auth) {
      throw new Error('Auth instance is null after initialization');
    }

    console.log('[HYDRA-IOT] Firebase Auth ready');
    return auth;

  } catch (error) {
    console.error('[HYDRA-IOT] CRITICAL: Failed to initialize Firebase Auth:', error);
    console.error('[HYDRA-IOT] Error details:', {
      message: error.message,
      code: error.code,
      stack: error.stack
    });

    // Try fallback initialization without persistence
    try {
      console.log('[HYDRA-IOT] Attempting fallback auth initialization...');
      const { getAuth } = await import('firebase/auth');
      auth = getAuth(app);
      console.log('[HYDRA-IOT] Fallback auth initialization successful');
      return auth;
    } catch (fallbackError) {
      console.error('[HYDRA-IOT] Fallback auth initialization also failed:', fallbackError);
      throw new Error(`Firebase Auth initialization failed: ${error.message}`);
    }
  }
};

// Initialize auth asynchronously
initializeFirebaseAuth().catch(error => {
  console.error('[HYDRA-IOT] Failed to initialize Firebase Auth:', error);
});

// Initialize other Firebase services
let database, firestore;
try {
  database = getDatabase(app);
  firestore = getFirestore(app);
  console.log('[HYDRA-IOT] Firebase services initialized successfully');
} catch (error) {
  console.error('[HYDRA-IOT] Error initializing Firebase services:', error);
  throw error;
}

// Export a function to get auth instance (since it's initialized asynchronously)
export const getAuthInstance = async () => {
  if (!auth) {
    auth = await initializeFirebaseAuth();
  }
  return auth;
};

export { app, database, firestore };

// For backward compatibility, export auth but it might be undefined initially
export { auth };
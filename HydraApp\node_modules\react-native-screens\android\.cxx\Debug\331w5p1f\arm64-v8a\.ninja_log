# ninja log v5
6	3976	7696119989571443	CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o	645589ad4be30012
11	3987	7696119989927527	CMakeFiles/rnscreens.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o	2e7e1170f0a7c39f
0	6262	7696120012724278	CMakeFiles/rnscreens.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp.o	e657af7383470a4a
22	6363	7696120013851768	CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o	15fc639e32e3ecd7
16	8258	7696120032892424	CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o	1ea8ff32e16da207
8259	8459	7696120035059682	../../../../build/intermediates/cxx/Debug/331w5p1f/obj/arm64-v8a/librnscreens.so	6e06036cb22483e3
0	11	0	clean	1ffa0f1d946a52ac
0	10	0	clean	1ffa0f1d946a52ac
391	3636	7701081399902780	CMakeFiles/rnscreens.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o	2e7e1170f0a7c39f
387	3785	7701081401060968	CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o	645589ad4be30012
383	10148	7701081465070884	CMakeFiles/rnscreens.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp.o	e657af7383470a4a
373	10386	7701081467535730	CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o	15fc639e32e3ecd7
378	13820	7701081501590821	CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o	1ea8ff32e16da207
13820	15673	7701081519555549	../../../../build/intermediates/cxx/Debug/331w5p1f/obj/arm64-v8a/librnscreens.so	6e06036cb22483e3
0	9	0	clean	1ffa0f1d946a52ac
35	2701	7701123548349042	CMakeFiles/rnscreens.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o	2e7e1170f0a7c39f
32	2847	7701123550045980	CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o	645589ad4be30012
30	4636	7701123567794549	CMakeFiles/rnscreens.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp.o	e657af7383470a4a
22	4687	7701123568559671	CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o	15fc639e32e3ecd7
26	6463	7701123586215618	CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o	1ea8ff32e16da207
6463	6687	7701123588632666	../../../../build/intermediates/cxx/Debug/331w5p1f/obj/arm64-v8a/librnscreens.so	6e06036cb22483e3
0	14	0	clean	1ffa0f1d946a52ac
48	2742	7701144291910339	CMakeFiles/rnscreens.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o	2e7e1170f0a7c39f
42	2789	7701144292555755	CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o	645589ad4be30012
45	4448	7701144309399145	CMakeFiles/rnscreens.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp.o	e657af7383470a4a
39	4735	7701144312374731	CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o	15fc639e32e3ecd7
32	7150	7701144336228724	CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o	1ea8ff32e16da207
7150	7421	7701144339239625	../../../../build/intermediates/cxx/Debug/331w5p1f/obj/arm64-v8a/librnscreens.so	6e06036cb22483e3
0	11	0	clean	1ffa0f1d946a52ac

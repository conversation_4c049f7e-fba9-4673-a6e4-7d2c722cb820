# C/C++ build system timings
generate_cxx_metadata
  [gap of 80ms]
  create-invalidation-state 39ms
  generate-prefab-packages
    [gap of 40ms]
    exec-prefab 1439ms
    [gap of 82ms]
  generate-prefab-packages completed in 1561ms
  execute-generate-process
    exec-configure 980ms
    [gap of 292ms]
  execute-generate-process completed in 1273ms
  [gap of 81ms]
  write-metadata-json-to-file 18ms
generate_cxx_metadata completed in 3057ms


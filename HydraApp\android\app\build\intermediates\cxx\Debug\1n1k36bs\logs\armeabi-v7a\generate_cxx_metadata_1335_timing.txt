# C/C++ build system timings
generate_cxx_metadata
  [gap of 108ms]
  create-invalidation-state 217ms
  generate-prefab-packages
    [gap of 208ms]
    exec-prefab 4599ms
    [gap of 136ms]
  generate-prefab-packages completed in 4943ms
  execute-generate-process
    exec-configure 1529ms
    [gap of 504ms]
  execute-generate-process completed in 2035ms
  [gap of 187ms]
  write-metadata-json-to-file 58ms
generate_cxx_metadata completed in 7574ms


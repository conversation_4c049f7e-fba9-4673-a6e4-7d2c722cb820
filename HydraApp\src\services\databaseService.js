import { ref, set, get, onValue, push, update, remove, query, orderByChild, orderByKey, startAt, endAt, limitToLast } from 'firebase/database';
import { collection, addDoc, getDoc, getDocs, updateDoc, deleteDoc, doc, setDoc, query as firestoreQuery, where, orderBy, limit as firestoreLimit, onSnapshot } from 'firebase/firestore';
import { database, firestore } from './firebase';

// Realtime Database Services

/**
 * Get current sensor data for a specific plant
 * @param {string} plantId - The ID of the plant (pot1, pot2, pot3)
 * @param {function} callback - Callback function to handle data updates
 * @returns {function} - Unsubscribe function
 */
export const subscribePlantSensorData = (plantId, callback) => {
  const sensorRef = ref(database, `${plantId}`);

  const unsubscribe = onValue(sensorRef, (snapshot) => {
    const data = snapshot.val() || {
      soilMoisture: 0,
      temperature: 0,
      humidity: 0
    };
    callback(data);
  });

  return unsubscribe;
};

/**
 * Get pump status for a specific plant
 * @param {string} plantId - The ID of the plant (pot1, pot2, pot3)
 * @param {function} callback - Callback function to handle data updates
 * @returns {function} - Unsubscribe function
 */
export const subscribePumpStatus = (plantId, callback) => {
  const pumpRef = ref(database, `${plantId}/pump`);

  const unsubscribe = onValue(pumpRef, (snapshot) => {
    const pumpStatus = snapshot.val();
    // Convert boolean to status object
    const data = {
      status: pumpStatus ? 'on' : 'off',
      lastUpdated: new Date().toISOString()
    };
    callback(data);
  });

  return unsubscribe;
};

/**
 * Toggle pump status for a specific plant
 * @param {string} plantId - The ID of the plant (pot1, pot2, pot3)
 * @param {boolean} isOn - Whether to turn the pump on or off
 * @returns {Promise} - Promise that resolves when the operation is complete
 */
export const togglePump = async (plantId, isOn) => {
  const pumpRef = ref(database, `${plantId}/pump`);

  // First set the pump status
  await set(pumpRef, isOn);

  // Also add a record to watering history if turning on
  if (isOn) {
    const timestamp = new Date().toISOString();
    const historyRef = push(ref(database, `${plantId}/wateringHistory`));
    await set(historyRef, {
      method: 'manual',
      timestamp: timestamp,
      duration: 10 // Default duration in seconds
    });
  }

  return true;
};

/**
 * Get watering history for a specific plant
 * @param {string} plantId - The ID of the plant (pot1, pot2, pot3)
 * @param {number} limit - Maximum number of records to retrieve
 * @param {function} callback - Callback function to handle data updates
 * @returns {function} - Unsubscribe function
 */
export const subscribeWateringHistory = (plantId, limitCount, callback) => {
  const historyRef = query(
    ref(database, `${plantId}/wateringHistory`),
    orderByChild('timestamp'),
    limitToLast(limitCount)
  );

  const unsubscribe = onValue(historyRef, (snapshot) => {
    const data = snapshot.val() || {};

    // Convert object to array and sort by timestamp
    const historyArray = Object.keys(data).map(key => ({
      id: key,
      ...data[key],
    })).sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    callback(historyArray);
  });

  return unsubscribe;
};

/**
 * Add a watering record to history
 * @param {string} plantId - The ID of the plant (pot1, pot2, pot3)
 * @param {string} method - The watering method ('manual' or 'automatic')
 * @param {number} duration - The duration of watering in seconds
 * @returns {Promise} - Promise that resolves when the operation is complete
 */
export const addWateringRecord = async (plantId, method, duration) => {
  const historyRef = ref(database, `${plantId}/wateringHistory`);

  const newRecordRef = push(historyRef);
  return set(newRecordRef, {
    method,
    duration,
    timestamp: new Date().toISOString(),
  });
};

// Firestore Services

/**
 * Get user profile data
 * @param {string} userId - The ID of the user
 * @returns {Promise} - Promise that resolves with user data
 */
export const getUserProfile = async (userId) => {
  const userDoc = doc(firestore, 'users', userId);
  const userSnapshot = await getDoc(userDoc);

  if (userSnapshot.exists()) {
    return userSnapshot.data();
  }

  return null;
};

/**
 * Create or update user profile
 * @param {string} userId - The ID of the user
 * @param {Object} userData - User profile data
 * @returns {Promise} - Promise that resolves when the operation is complete
 */
export const updateUserProfile = async (userId, userData) => {
  const userDoc = doc(firestore, 'users', userId);
  return setDoc(userDoc, userData, { merge: true });
};

/**
 * Get plant settings for a user
 * @param {string} userId - The ID of the user
 * @param {string} plantId - The ID of the plant
 * @returns {Promise} - Promise that resolves with plant settings
 */
export const getPlantSettings = async (userId, plantId) => {
  const settingsDoc = doc(firestore, 'users', userId, 'plantSettings', plantId);
  const settingsSnapshot = await getDoc(settingsDoc);

  if (settingsSnapshot.exists()) {
    return settingsSnapshot.data();
  }

  return null;
};

/**
 * Update plant settings
 * @param {string} userId - The ID of the user
 * @param {string} plantId - The ID of the plant
 * @param {Object} settings - Plant settings data
 * @returns {Promise} - Promise that resolves when the operation is complete
 */
export const updatePlantSettings = async (userId, plantId, settings) => {
  const settingsDoc = doc(firestore, 'users', userId, 'plantSettings', plantId);
  return setDoc(settingsDoc, settings, { merge: true });
};

/**
 * Get all pots from the realtime database with availability status
 * @param {string} excludePlantId - Optional plant ID to exclude from occupancy check (for editing)
 * @returns {Promise<Array>} - Promise that resolves with all pots and their status
 */
export const getAvailablePots = async (excludePlantId = null) => {
  try {
    const snapshot = await get(ref(database));
    const data = snapshot.val() || {};

    const pots = [];
    Object.keys(data).forEach(potId => {
      if (potId.startsWith('pot')) {
        const isOccupied = data[potId].occupied || false;
        const occupyingPlantId = data[potId].plantId || null;

        // Pot is available if:
        // 1. Not occupied, OR
        // 2. Occupied by the plant we're editing (excludePlantId)
        const available = !isOccupied || (excludePlantId && occupyingPlantId === excludePlantId);

        pots.push({
          id: potId,
          name: `Pot ${potId.replace('pot', '')}`,
          available,
          occupied: isOccupied,
          occupyingPlantId,
          sensorData: {
            soilMoisture: data[potId].soilMoisture || 0,
            temperature: data[potId].temperature || 0,
            humidity: data[potId].humidity || 0,
          }
        });
      }
    });

    return pots;
  } catch (error) {
    console.error('Error getting available pots:', error);
    throw error;
  }
};

/**
 * Add a new plant to the user's collection
 * @param {string} userId - The ID of the user
 * @param {Object} plantData - Plant data including conditions and pot assignment
 * @returns {Promise} - Promise that resolves when the operation is complete
 */
export const addPlant = async (userId, plantData) => {
  try {
    // Generate a unique plant ID
    const plantId = `plant_${Date.now()}`;

    // Save plant data to Firestore
    const plantDoc = doc(firestore, 'users', userId, 'plants', plantId);
    await setDoc(plantDoc, {
      ...plantData,
      id: plantId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });

    // Mark the pot as occupied in the realtime database
    if (plantData.potId) {
      const potRef = ref(database, `${plantData.potId}/occupied`);
      await set(potRef, true);

      // Also store the plant ID in the pot data
      const plantIdRef = ref(database, `${plantData.potId}/plantId`);
      await set(plantIdRef, plantId);
    }

    return plantId;
  } catch (error) {
    console.error('Error adding plant:', error);
    throw error;
  }
};

/**
 * Get all plants for a user
 * @param {string} userId - The ID of the user
 * @returns {Promise<Array>} - Promise that resolves with user's plants
 */
export const getUserPlants = async (userId) => {
  try {
    const plantsCollection = collection(firestore, 'users', userId, 'plants');
    const plantsSnapshot = await getDocs(plantsCollection);

    const plants = [];
    plantsSnapshot.forEach(doc => {
      plants.push({
        id: doc.id,
        ...doc.data(),
      });
    });

    return plants;
  } catch (error) {
    console.error('Error getting user plants:', error);
    throw error;
  }
};

/**
 * Update plant data
 * @param {string} userId - The ID of the user
 * @param {string} plantId - The ID of the plant
 * @param {Object} plantData - Updated plant data
 * @param {string} oldPotId - Previous pot ID (if pot is being changed)
 * @returns {Promise} - Promise that resolves when the operation is complete
 */
export const updatePlant = async (userId, plantId, plantData, oldPotId = null) => {
  try {
    // Update plant data in Firestore
    const plantDoc = doc(firestore, 'users', userId, 'plants', plantId);
    await setDoc(plantDoc, {
      ...plantData,
      updatedAt: new Date().toISOString(),
    }, { merge: true });

    // Handle pot changes if necessary
    if (oldPotId && oldPotId !== plantData.potId) {
      // Free up the old pot
      const oldPotRef = ref(database, `${oldPotId}/occupied`);
      await set(oldPotRef, false);

      const oldPlantIdRef = ref(database, `${oldPotId}/plantId`);
      await set(oldPlantIdRef, null);

      // Occupy the new pot
      if (plantData.potId) {
        const newPotRef = ref(database, `${plantData.potId}/occupied`);
        await set(newPotRef, true);

        const newPlantIdRef = ref(database, `${plantData.potId}/plantId`);
        await set(newPlantIdRef, plantId);
      }
    } else if (plantData.potId && !oldPotId) {
      // New pot assignment (plant didn't have a pot before)
      const potRef = ref(database, `${plantData.potId}/occupied`);
      await set(potRef, true);

      const plantIdRef = ref(database, `${plantData.potId}/plantId`);
      await set(plantIdRef, plantId);
    }

    return true;
  } catch (error) {
    console.error('Error updating plant:', error);
    throw error;
  }
};

/**
 * Delete a plant and free up its pot
 * @param {string} userId - The ID of the user
 * @param {string} plantId - The ID of the plant
 * @param {string} potId - The ID of the pot to free up
 * @returns {Promise} - Promise that resolves when the operation is complete
 */
export const deletePlant = async (userId, plantId, potId) => {
  try {
    // Delete plant from Firestore
    const plantDoc = doc(firestore, 'users', userId, 'plants', plantId);
    await deleteDoc(plantDoc);

    // Free up the pot in the realtime database
    if (potId) {
      const potRef = ref(database, `${potId}/occupied`);
      await set(potRef, false);

      const plantIdRef = ref(database, `${potId}/plantId`);
      await set(plantIdRef, null);
    }

    return true;
  } catch (error) {
    console.error('Error deleting plant:', error);
    throw error;
  }
};

/**
 * Check if plant needs watering based on conditions
 * @param {Object} sensorData - Current sensor readings
 * @param {Object} conditions - Plant's ideal conditions
 * @returns {boolean} - Whether the plant needs watering
 */
export const checkPlantNeedsWatering = (sensorData, conditions) => {
  if (!sensorData || !conditions) return false;

  const { soilMoisture, temperature, humidity } = sensorData;
  const { soilMoisture: soilRange, temperature: tempRange, humidity: humidityRange } = conditions;

  // Check if any condition is outside the ideal range
  const soilTooLow = soilMoisture < soilRange.min;
  const tempOutOfRange = temperature < tempRange.min || temperature > tempRange.max;
  const humidityTooLow = humidity < humidityRange.min;

  // Plant needs attention if soil is too dry or other conditions are poor
  return soilTooLow || tempOutOfRange || humidityTooLow;
};

/**
 * Trigger manual watering for a specific pot
 * @param {string} potId - The ID of the pot to water
 * @param {number} duration - Duration in seconds (optional, default 5)
 * @returns {Promise} - Promise that resolves when the operation is complete
 */
export const triggerManualWatering = async (potId, duration = 5) => {
  try {
    // Set pump to true to start watering
    const pumpRef = ref(database, `${potId}/pump`);
    await set(pumpRef, true);

    // Set watering duration
    const durationRef = ref(database, `${potId}/wateringDuration`);
    await set(durationRef, duration);

    // Set manual watering flag
    const manualRef = ref(database, `${potId}/manualWatering`);
    await set(manualRef, true);

    // Set timestamp
    const timestampRef = ref(database, `${potId}/lastWateringTime`);
    await set(timestampRef, new Date().toISOString());

    return true;
  } catch (error) {
    console.error('Error triggering manual watering:', error);
    throw error;
  }
};

/**
 * Stop watering for a specific pot
 * @param {string} potId - The ID of the pot to stop watering
 * @returns {Promise} - Promise that resolves when the operation is complete
 */
export const stopWatering = async (potId) => {
  try {
    // Set pump to false to stop watering
    const pumpRef = ref(database, `${potId}/pump`);
    await set(pumpRef, false);

    // Clear manual watering flag
    const manualRef = ref(database, `${potId}/manualWatering`);
    await set(manualRef, false);

    return true;
  } catch (error) {
    console.error('Error stopping watering:', error);
    throw error;
  }
};

/**
 * Enable/disable automatic watering for a plant
 * @param {string} userId - The ID of the user
 * @param {string} plantId - The ID of the plant
 * @param {boolean} enabled - Whether to enable automatic watering
 * @returns {Promise} - Promise that resolves when the operation is complete
 */
export const setAutomaticWatering = async (userId, plantId, enabled) => {
  try {
    const plantDoc = doc(firestore, 'users', userId, 'plants', plantId);
    await setDoc(plantDoc, {
      automaticWatering: enabled,
      updatedAt: new Date().toISOString(),
    }, { merge: true });

    return true;
  } catch (error) {
    console.error('Error setting automatic watering:', error);
    throw error;
  }
};

/**
 * Enable/disable interval-based automatic watering for a plant
 * @param {string} userId - The ID of the user
 * @param {string} plantId - The ID of the plant
 * @param {boolean} enabled - Whether to enable interval watering
 * @param {number} intervalHours - Watering interval in hours (optional)
 * @returns {Promise} - Promise that resolves when the operation is complete
 */
export const setIntervalWatering = async (userId, plantId, enabled, intervalHours = 24) => {
  try {
    const plantDoc = doc(firestore, 'users', userId, 'plants', plantId);
    await setDoc(plantDoc, {
      intervalWatering: enabled,
      wateringInterval: intervalHours,
      lastIntervalWatering: enabled ? new Date().toISOString() : null,
      updatedAt: new Date().toISOString(),
    }, { merge: true });

    return true;
  } catch (error) {
    console.error('Error setting interval watering:', error);
    throw error;
  }
};

/**
 * Update watering interval for a plant
 * @param {string} userId - The ID of the user
 * @param {string} plantId - The ID of the plant
 * @param {number} intervalHours - New interval in hours
 * @returns {Promise} - Promise that resolves when the operation is complete
 */
export const updateWateringInterval = async (userId, plantId, intervalHours) => {
  try {
    const plantDoc = doc(firestore, 'users', userId, 'plants', plantId);
    await setDoc(plantDoc, {
      wateringInterval: intervalHours,
      updatedAt: new Date().toISOString(),
    }, { merge: true });

    return true;
  } catch (error) {
    console.error('Error updating watering interval:', error);
    throw error;
  }
};

/**
 * Check if plant needs interval watering
 * @param {Object} plant - Plant data with interval settings
 * @returns {boolean} - Whether the plant needs interval watering
 */
export const checkIntervalWateringDue = (plant) => {
  if (!plant.intervalWatering || !plant.wateringInterval || !plant.lastIntervalWatering) {
    return false;
  }

  const lastWatering = new Date(plant.lastIntervalWatering);
  const now = new Date();
  const intervalMs = plant.wateringInterval * 60 * 60 * 1000; // Convert hours to milliseconds

  return (now.getTime() - lastWatering.getTime()) >= intervalMs;
};

/**
 * Update last interval watering time
 * @param {string} userId - The ID of the user
 * @param {string} plantId - The ID of the plant
 * @returns {Promise} - Promise that resolves when the operation is complete
 */
export const updateLastIntervalWatering = async (userId, plantId) => {
  try {
    const plantDoc = doc(firestore, 'users', userId, 'plants', plantId);
    await setDoc(plantDoc, {
      lastIntervalWatering: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }, { merge: true });

    return true;
  } catch (error) {
    console.error('Error updating last interval watering:', error);
    throw error;
  }
};

/**
 * Get count of active pumps (currently watering)
 * @returns {Promise<number>} - Promise that resolves with the number of active pumps
 */
export const getActivePumpsCount = async () => {
  try {
    const snapshot = await get(ref(database));
    const data = snapshot.val() || {};

    let activePumpsCount = 0;
    Object.keys(data).forEach(potId => {
      if (potId.startsWith('pot')) {
        const pumpStatus = data[potId].pump || false;
        if (pumpStatus) {
          activePumpsCount++;
        }
      }
    });

    return activePumpsCount;
  } catch (error) {
    console.error('Error getting active pumps count:', error);
    return 0;
  }
};

/**
 * Subscribe to active pumps count changes
 * @param {Function} callback - Callback function to handle count updates
 * @returns {Function} - Unsubscribe function
 */
export const subscribeToActivePumps = (callback) => {
  try {
    const dbRef = ref(database);

    const unsubscribe = onValue(dbRef, (snapshot) => {
      const data = snapshot.val() || {};

      let activePumpsCount = 0;
      Object.keys(data).forEach(potId => {
        if (potId.startsWith('pot')) {
          const pumpStatus = data[potId].pump || false;
          if (pumpStatus) {
            activePumpsCount++;
          }
        }
      });

      callback(activePumpsCount);
    });

    return unsubscribe;
  } catch (error) {
    console.error('Error subscribing to active pumps:', error);
    return () => {}; // Return empty function as fallback
  }
};

/**
 * Get watering status for a pot
 * @param {string} potId - The ID of the pot
 * @returns {Promise<Object>} - Promise that resolves with watering status
 */
export const getWateringStatus = async (potId) => {
  try {
    const snapshot = await get(ref(database, potId));
    const data = snapshot.val() || {};

    return {
      isWatering: data.pump || false,
      isManual: data.manualWatering || false,
      duration: data.wateringDuration || 0,
      lastWateringTime: data.lastWateringTime || null,
    };
  } catch (error) {
    console.error('Error getting watering status:', error);
    throw error;
  }
};

/**
 * Add watering record to history
 * @param {string} userId - The ID of the user
 * @param {string} plantId - The ID of the plant
 * @param {string} potId - The ID of the pot
 * @param {Object} wateringData - Watering data (type, duration, etc.)
 * @returns {Promise} - Promise that resolves when the operation is complete
 */
export const addWateringHistory = async (userId, plantId, potId, wateringData) => {
  try {
    const historyCollection = collection(firestore, 'users', userId, 'wateringHistory');
    await addDoc(historyCollection, {
      plantId,
      potId,
      timestamp: new Date().toISOString(),
      ...wateringData,
    });

    return true;
  } catch (error) {
    console.error('Error adding watering history:', error);
    throw error;
  }
};

/**
 * Store historical sensor data
 * @param {string} potId - The ID of the pot
 * @param {Object} sensorData - The sensor data to store
 * @returns {Promise} - Promise that resolves when data is stored
 */
export const storeHistoricalData = async (potId, sensorData) => {
  try {
    const timestamp = new Date().toISOString();
    const historyRef = ref(database, `history/${potId}/${timestamp}`);

    await set(historyRef, {
      ...sensorData,
      timestamp,
    });

    // Keep only last 30 days of data to prevent database bloat
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const oldDataQuery = query(
      ref(database, `history/${potId}`),
      orderByKey(),
      endAt(thirtyDaysAgo.toISOString())
    );

    const oldDataSnapshot = await get(oldDataQuery);
    if (oldDataSnapshot.exists()) {
      const updates = {};
      oldDataSnapshot.forEach((child) => {
        updates[`history/${potId}/${child.key}`] = null;
      });
      await update(ref(database), updates);
    }

    return true;
  } catch (error) {
    console.error('Error storing historical data:', error);
    throw error;
  }
};

/**
 * Get historical sensor data for a pot
 * @param {string} potId - The ID of the pot
 * @param {string} timeRange - Time range ('day', 'week', 'month')
 * @returns {Promise<Array>} - Promise that resolves with historical data
 */
export const getHistoricalData = async (potId, timeRange = 'day') => {
  try {
    const now = new Date();
    let startTime;

    switch (timeRange) {
      case 'day':
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'week':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    const historyQuery = query(
      ref(database, `history/${potId}`),
      orderByKey(),
      startAt(startTime.toISOString()),
      endAt(now.toISOString())
    );

    const snapshot = await get(historyQuery);
    const data = [];

    if (snapshot.exists()) {
      snapshot.forEach((child) => {
        data.push({
          ...child.val(),
          key: child.key,
        });
      });
    }

    // If no historical data, return current data as single point
    if (data.length === 0) {
      const currentData = await get(ref(database, potId));
      if (currentData.exists()) {
        const current = currentData.val();
        data.push({
          soilMoisture: current.soilMoisture || 0,
          temperature: current.temperature || 0,
          humidity: current.humidity || 0,
          timestamp: now.toISOString(),
        });
      }
    }

    return data;
  } catch (error) {
    console.error('Error getting historical data:', error);
    return [];
  }
};

/**
 * Subscribe to sensor data and automatically store historical data
 * @param {string} potId - The ID of the pot
 * @param {Function} callback - Callback function to handle real-time data
 * @returns {Function} - Unsubscribe function
 */
export const subscribeAndStoreData = (potId, callback) => {
  try {
    let lastStoredTime = 0;
    const STORE_INTERVAL = 5 * 60 * 1000; // Store data every 5 minutes

    const unsubscribe = onValue(ref(database, potId), (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const now = Date.now();

        // Store historical data every 5 minutes
        if (now - lastStoredTime > STORE_INTERVAL) {
          storeHistoricalData(potId, {
            soilMoisture: data.soilMoisture || 0,
            temperature: data.temperature || 0,
            humidity: data.humidity || 0,
          }).catch(error => {
            console.error('Error auto-storing historical data:', error);
          });
          lastStoredTime = now;
        }

        // Call the callback with real-time data
        callback(data);
      }
    });

    return unsubscribe;
  } catch (error) {
    console.error('Error subscribing and storing data:', error);
    return () => {}; // Return empty function as fallback
  }
};

/**
 * Get watering history with filtering options
 * @param {string} userId - The ID of the user
 * @param {Object} filters - Filter options
 * @returns {Promise<Array>} - Promise that resolves with filtered watering history
 */
export const getWateringHistory = async (userId, filters = {}) => {
  try {
    const {
      plantId = null,
      potId = null,
      method = null, // 'manual', 'automatic', 'interval'
      dateRange = 'week', // 'day', 'week', 'month', 'all'
      limit = 50,
    } = filters;

    // Start with a simple query - just order by timestamp
    let historyQuery = firestoreQuery(
      collection(firestore, 'users', userId, 'wateringHistory'),
      orderBy('timestamp', 'desc'),
      firestoreLimit(limit * 2) // Get more records to filter client-side
    );

    // If we have a specific plant, add that filter (single field index)
    if (plantId) {
      historyQuery = firestoreQuery(
        collection(firestore, 'users', userId, 'wateringHistory'),
        where('plantId', '==', plantId),
        orderBy('timestamp', 'desc'),
        firestoreLimit(limit * 2)
      );
    }

    const snapshot = await getDocs(historyQuery);
    let history = [];

    snapshot.forEach((doc) => {
      history.push({
        id: doc.id,
        ...doc.data(),
      });
    });

    // Apply client-side filtering for other criteria
    if (dateRange !== 'all') {
      const now = new Date();
      let startDate;

      switch (dateRange) {
        case 'day':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      }

      history = history.filter(item =>
        new Date(item.timestamp) >= startDate
      );
    }

    // Apply pot filter client-side
    if (potId) {
      history = history.filter(item => item.potId === potId);
    }

    // Apply method filter client-side
    if (method) {
      history = history.filter(item => item.method === method);
    }

    // Apply final limit
    history = history.slice(0, limit);

    return history;
  } catch (error) {
    console.error('Error getting watering history:', error);
    return [];
  }
};

/**
 * Subscribe to watering history with real-time updates
 * @param {string} userId - The ID of the user
 * @param {Object} filters - Filter options
 * @param {Function} callback - Callback function to handle updates
 * @returns {Function} - Unsubscribe function
 */
export const subscribeToWateringHistory = (userId, filters, callback) => {
  try {
    const {
      plantId = null,
      potId = null,
      method = null,
      dateRange = 'week',
      limit = 50
    } = filters;

    // Use simple query to avoid composite index requirements
    let historyQuery = firestoreQuery(
      collection(firestore, 'users', userId, 'wateringHistory'),
      orderBy('timestamp', 'desc'),
      firestoreLimit(limit * 2) // Get more records for client-side filtering
    );

    // If we have a specific plant, use that filter (single field index)
    if (plantId) {
      historyQuery = firestoreQuery(
        collection(firestore, 'users', userId, 'wateringHistory'),
        where('plantId', '==', plantId),
        orderBy('timestamp', 'desc'),
        firestoreLimit(limit * 2)
      );
    }

    const unsubscribe = onSnapshot(historyQuery, (snapshot) => {
      let history = [];
      snapshot.forEach((doc) => {
        history.push({
          id: doc.id,
          ...doc.data(),
        });
      });

      // Apply client-side filtering
      if (dateRange !== 'all') {
        const now = new Date();
        let startDate;

        switch (dateRange) {
          case 'day':
            startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            break;
          case 'week':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case 'month':
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
          default:
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        }

        history = history.filter(item =>
          new Date(item.timestamp) >= startDate
        );
      }

      // Apply pot filter client-side
      if (potId) {
        history = history.filter(item => item.potId === potId);
      }

      // Apply method filter client-side
      if (method) {
        history = history.filter(item => item.method === method);
      }

      // Apply final limit
      history = history.slice(0, limit);

      callback(history);
    });

    return unsubscribe;
  } catch (error) {
    console.error('Error subscribing to watering history:', error);
    return () => {}; // Return empty function as fallback
  }
};

const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('@react-native/metro-config').MetroConfig}
 */
const config = {
  resolver: {
    // Add support for .cjs files (needed for Firebase v11+)
    sourceExts: ['js', 'jsx', 'ts', 'tsx', 'json', 'cjs'],
    // Disable package exports to avoid Firebase module resolution issues
    unstable_enablePackageExports: false,
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);

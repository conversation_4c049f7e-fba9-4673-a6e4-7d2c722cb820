import { useState, useEffect } from 'react';

interface FirebaseInitState {
  isReady: boolean;
  error: string | null;
}

/**
 * Hook to ensure Firebase is properly initialized before the app starts
 * @returns {FirebaseInitState} - { isReady: boolean, error: string|null }
 */
export const useFirebaseInit = (): FirebaseInitState => {
  const [isReady, setIsReady] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeFirebase = async () => {
      try {
        console.log('[HYDRA-IOT] Checking Firebase initialization...');

        // Import Firebase services to trigger initialization
        const firebaseModule = await import('../services/firebase');
        const { getAuthInstance } = firebaseModule;
        const database: any = firebaseModule.database;
        const firestore: any = firebaseModule.firestore;

        // Verify all services are available
        const auth = await getAuthInstance();
        if (!auth) {
          throw new Error('Firebase Auth is not initialized');
        }
        if (!database) {
          throw new Error('Firebase Database is not initialized');
        }
        if (!firestore) {
          throw new Error('Firebase Firestore is not initialized');
        }

        console.log('[HYDRA-IOT] Firebase initialization verified successfully');
        setIsReady(true);
        setError(null);
      } catch (err: any) {
        console.error('[HYDRA-IOT] Firebase initialization failed:', err);
        setError(err?.message || 'Unknown error occurred');
        setIsReady(false);
      }
    };

    initializeFirebase();
  }, []);

  return { isReady, error };
};

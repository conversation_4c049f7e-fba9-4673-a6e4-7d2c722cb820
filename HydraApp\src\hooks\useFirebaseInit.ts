import { useState, useEffect } from 'react';

interface FirebaseInitState {
  isReady: boolean;
  error: string | null;
}

/**
 * Hook to ensure Firebase is properly initialized before the app starts
 * @returns {FirebaseInitState} - { isReady: boolean, error: string|null }
 */
export const useFirebaseInit = (): FirebaseInitState => {
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const initializeFirebase = async () => {
      try {
        console.log('[HYDRA-IOT] Checking Firebase initialization...');

        // Import Firebase services to trigger initialization
        const { auth, database, firestore } = await import('../services/firebase');

        // Verify all services are available
        if (!auth) {
          throw new Error('Firebase Auth is not initialized');
        }
        if (!database) {
          throw new Error('Firebase Database is not initialized');
        }
        if (!firestore) {
          throw new Error('Firebase Firestore is not initialized');
        }

        console.log('[HYDRA-IOT] Firebase initialization verified successfully');
        setIsReady(true);
        setError(null);
      } catch (err) {
        console.error('[HYDRA-IOT] Firebase initialization failed:', err);
        setError(err.message);
        setIsReady(false);
      }
    };

    initializeFirebase();
  }, []);

  return { isReady, error };
};

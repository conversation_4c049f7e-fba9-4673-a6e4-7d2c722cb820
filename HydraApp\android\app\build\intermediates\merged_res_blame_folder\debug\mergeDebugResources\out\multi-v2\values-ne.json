{"logs": [{"outputFile": "com.hydraiot.app-mergeDebugResources-51:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79f6ab95733be72bb6af37d95e29f537\\transformed\\browser-1.4.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,274,388", "endColumns": "106,111,113,107", "endOffsets": "157,269,383,491"}, "to": {"startLines": "65,68,69,70", "startColumns": "4,4,4,4", "startOffsets": "6762,7031,7143,7257", "endColumns": "106,111,113,107", "endOffsets": "6864,7138,7252,7360"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af63e50a53e992ea9c751fc2deb2fc57\\transformed\\appcompat-1.7.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2254,2367,2477,2594,2761,2872", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2249,2362,2472,2589,2756,2867,2947"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "267,376,487,595,686,793,913,997,1076,1167,1260,1355,1449,1549,1642,1737,1831,1922,2013,2099,2212,2313,2416,2529,2639,2756,2923,11511", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "371,482,590,681,788,908,992,1071,1162,1255,1350,1444,1544,1637,1732,1826,1917,2008,2094,2207,2308,2411,2524,2634,2751,2918,3029,11586"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3135f157d17fbe3966fe84b667792db9\\transformed\\core-1.13.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "36,37,38,39,40,41,42,126", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3339,3442,3545,3647,3753,3851,3951,11730", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "3437,3540,3642,3748,3846,3946,4054,11826"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0a1c3fd62b5fb636be47a17086a4f32c\\transformed\\play-services-basement-18.5.0\\res\\values-ne\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "55", "startColumns": "4", "startOffsets": "5477", "endColumns": "163", "endOffsets": "5636"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7312d9ce0e64b42e563cee99ce0c7cbc\\transformed\\play-services-base-18.5.0\\res\\values-ne\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,454,584,697,864,996,1102,1203,1379,1489,1649,1778,1922,2070,2132,2200", "endColumns": "106,153,129,112,166,131,105,100,175,109,159,128,143,147,61,67,87", "endOffsets": "299,453,583,696,863,995,1101,1202,1378,1488,1648,1777,1921,2069,2131,2199,2287"}, "to": {"startLines": "47,48,49,50,51,52,53,54,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4435,4546,4704,4838,4955,5126,5262,5372,5641,5821,5935,6099,6232,6380,6532,6598,6670", "endColumns": "110,157,133,116,170,135,109,104,179,113,163,132,147,151,65,71,91", "endOffsets": "4541,4699,4833,4950,5121,5257,5367,5472,5816,5930,6094,6227,6375,6527,6593,6665,6757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa0e231f4d27dc8a031f0119595b6f1\\transformed\\material-1.6.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,217,299,401,511,598,664,760,826,887,992,1064,1122,1196,1258,1312,1425,1485,1546,1605,1683,1807,1888,1973,2079,2160,2243,2326,2393,2459,2536,2615,2703,2772,2848,2929,2997,3088,3166,3259,3356,3430,3509,3607,3667,3733,3821,3909,3971,4039,4102,4207,4325,4420,4540", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,101,109,86,65,95,65,60,104,71,57,73,61,53,112,59,60,58,77,123,80,84,105,80,82,82,66,65,76,78,87,68,75,80,67,90,77,92,96,73,78,97,59,65,87,87,61,67,62,104,117,94,119,83", "endOffsets": "212,294,396,506,593,659,755,821,882,987,1059,1117,1191,1253,1307,1420,1480,1541,1600,1678,1802,1883,1968,2074,2155,2238,2321,2388,2454,2531,2610,2698,2767,2843,2924,2992,3083,3161,3254,3351,3425,3504,3602,3662,3728,3816,3904,3966,4034,4097,4202,4320,4415,4535,4619"}, "to": {"startLines": "2,35,43,44,45,66,67,71,74,76,77,78,79,80,81,82,83,84,85,86,87,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3257,4059,4161,4271,6869,6935,7365,7578,7707,7812,7884,7942,8016,8078,8132,8245,8305,8366,8425,8503,8694,8775,8860,8966,9047,9130,9213,9280,9346,9423,9502,9590,9659,9735,9816,9884,9975,10053,10146,10243,10317,10396,10494,10554,10620,10708,10796,10858,10926,10989,11094,11212,11307,11427", "endLines": "5,35,43,44,45,66,67,71,74,76,77,78,79,80,81,82,83,84,85,86,87,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "12,81,101,109,86,65,95,65,60,104,71,57,73,61,53,112,59,60,58,77,123,80,84,105,80,82,82,66,65,76,78,87,68,75,80,67,90,77,92,96,73,78,97,59,65,87,87,61,67,62,104,117,94,119,83", "endOffsets": "262,3334,4156,4266,4353,6930,7026,7426,7634,7807,7879,7937,8011,8073,8127,8240,8300,8361,8420,8498,8622,8770,8855,8961,9042,9125,9208,9275,9341,9418,9497,9585,9654,9730,9811,9879,9970,10048,10141,10238,10312,10391,10489,10549,10615,10703,10791,10853,10921,10984,11089,11207,11302,11422,11506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ff5b0ce559ae890269420b3dec0e4060\\transformed\\react-android-0.79.2-debug\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,132,200,279,347,414,484", "endColumns": "76,67,78,67,66,69,68", "endOffsets": "127,195,274,342,409,479,548"}, "to": {"startLines": "46,72,73,75,88,124,125", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4358,7431,7499,7639,8627,11591,11661", "endColumns": "76,67,78,67,66,69,68", "endOffsets": "4430,7494,7573,7702,8689,11656,11725"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd1664c1c7ce01ff711fc15460fc5485\\transformed\\credentials-1.2.0-rc01\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,111", "endOffsets": "161,273"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3034,3145", "endColumns": "110,111", "endOffsets": "3140,3252"}}]}]}
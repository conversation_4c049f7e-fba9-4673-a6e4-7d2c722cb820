import { getUserPlants, subscribePlantSensorData } from './databaseService';
import PushNotificationService from './pushNotificationService';

class PlantAlertService {
  constructor() {
    this.isMonitoring = false;
    this.plantSubscriptions = new Map();
    this.alertHistory = new Map();
    this.alertCooldown = 30 * 60 * 1000; // 30 minutes cooldown between same alerts
  }

  /**
   * Start monitoring all user plants for alerts
   */
  async startMonitoring(userId, userSettings) {
    try {
      console.log('PlantAlertService: Starting plant monitoring...');
      
      // Check if alert notifications are enabled
      if (!userSettings?.alertNotificationsEnabled) {
        console.log('PlantAlertService: Alert notifications disabled');
        return false;
      }

      // Get user plants
      const plants = await getUserPlants(userId);
      if (plants.length === 0) {
        console.log('PlantAlertService: No plants to monitor');
        return false;
      }

      // Subscribe to each plant's sensor data
      plants.forEach(plant => {
        if (plant.potId && plant.conditions) {
          this.subscribePlantSensorData(plant);
        }
      });

      this.isMonitoring = true;
      console.log(`PlantAlertService: Monitoring ${plants.length} plants`);
      return true;
    } catch (error) {
      console.error('PlantAlertService: Failed to start monitoring:', error);
      return false;
    }
  }

  /**
   * Stop monitoring all plants
   */
  stopMonitoring() {
    console.log('PlantAlertService: Stopping plant monitoring...');
    
    // Unsubscribe from all plant sensor data
    this.plantSubscriptions.forEach((unsubscribe, plantId) => {
      unsubscribe();
    });
    
    this.plantSubscriptions.clear();
    this.isMonitoring = false;
    console.log('PlantAlertService: Monitoring stopped');
  }

  /**
   * Subscribe to individual plant sensor data
   */
  subscribePlantSensorData(plant) {
    const { id: plantId, potId, conditions, name } = plant;
    
    try {
      const unsubscribe = subscribePlantSensorData(potId, (sensorData) => {
        this.checkPlantConditions(plant, sensorData);
      });
      
      this.plantSubscriptions.set(plantId, unsubscribe);
      console.log(`PlantAlertService: Subscribed to ${name} (${potId})`);
    } catch (error) {
      console.error(`PlantAlertService: Failed to subscribe to ${plantId}:`, error);
    }
  }

  /**
   * Check plant conditions and trigger alerts if needed
   */
  checkPlantConditions(plant, sensorData) {
    const { id: plantId, name, conditions } = plant;
    const { soilMoisture, temperature, humidity } = sensorData;
    
    const alerts = [];

    // Check soil moisture (critical alert)
    if (soilMoisture < conditions.soilMoisture.min) {
      alerts.push({
        type: 'low_soil_moisture',
        severity: 'critical',
        title: `${name} Needs Water!`,
        message: `Soil moisture is ${soilMoisture}% (below ${conditions.soilMoisture.min}%)`,
        icon: 'water-alert',
        color: '#D32F2F',
      });
    } else if (soilMoisture > conditions.soilMoisture.max) {
      alerts.push({
        type: 'high_soil_moisture',
        severity: 'warning',
        title: `${name} Too Wet`,
        message: `Soil moisture is ${soilMoisture}% (above ${conditions.soilMoisture.max}%)`,
        icon: 'water-off',
        color: '#F57C00',
      });
    }

    // Check temperature
    if (temperature < conditions.temperature.min) {
      alerts.push({
        type: 'low_temperature',
        severity: 'warning',
        title: `${name} Too Cold`,
        message: `Temperature is ${temperature}°C (below ${conditions.temperature.min}°C)`,
        icon: 'thermometer-low',
        color: '#1976D2',
      });
    } else if (temperature > conditions.temperature.max) {
      alerts.push({
        type: 'high_temperature',
        severity: 'warning',
        title: `${name} Too Hot`,
        message: `Temperature is ${temperature}°C (above ${conditions.temperature.max}°C)`,
        icon: 'thermometer-high',
        color: '#F57C00',
      });
    }

    // Check humidity
    if (humidity < conditions.humidity.min) {
      alerts.push({
        type: 'low_humidity',
        severity: 'info',
        title: `${name} Air Too Dry`,
        message: `Humidity is ${humidity}% (below ${conditions.humidity.min}%)`,
        icon: 'air-humidifier-off',
        color: '#00ACC1',
      });
    } else if (humidity > conditions.humidity.max) {
      alerts.push({
        type: 'high_humidity',
        severity: 'info',
        title: `${name} Air Too Humid`,
        message: `Humidity is ${humidity}% (above ${conditions.humidity.max}%)`,
        icon: 'water-percent',
        color: '#00ACC1',
      });
    }

    // Process alerts
    alerts.forEach(alert => {
      this.processAlert(plantId, alert);
    });
  }

  /**
   * Process and send alert if not in cooldown
   */
  processAlert(plantId, alert) {
    const alertKey = `${plantId}_${alert.type}`;
    const now = Date.now();
    const lastAlert = this.alertHistory.get(alertKey);

    // Check cooldown period
    if (lastAlert && (now - lastAlert) < this.alertCooldown) {
      console.log(`PlantAlertService: Alert ${alertKey} in cooldown`);
      return;
    }

    // Send push notification
    this.sendPushNotification(plantId, alert);
    
    // Update alert history
    this.alertHistory.set(alertKey, now);
    
    console.log(`PlantAlertService: Alert sent for ${alertKey}:`, alert.title);
  }

  /**
   * Send push notification (placeholder for server-side implementation)
   */
  sendPushNotification(plantId, alert) {
    // In a real implementation, this would call your backend API
    // which would send the push notification via Firebase Admin SDK
    
    console.log('PlantAlertService: Push notification data:', {
      plantId,
      type: 'plant_alert',
      alertType: alert.type,
      severity: alert.severity,
      title: alert.title,
      message: alert.message,
      icon: alert.icon,
      color: alert.color,
      timestamp: new Date().toISOString(),
    });

    // For testing, you can manually trigger notifications from Firebase console
    // using the data structure above
  }

  /**
   * Get alert statistics
   */
  getAlertStats() {
    return {
      isMonitoring: this.isMonitoring,
      monitoredPlants: this.plantSubscriptions.size,
      alertHistory: Array.from(this.alertHistory.entries()).map(([key, timestamp]) => ({
        alertKey: key,
        lastTriggered: new Date(timestamp).toISOString(),
      })),
    };
  }

  /**
   * Clear alert history for a plant
   */
  clearPlantAlertHistory(plantId) {
    const keysToDelete = [];
    this.alertHistory.forEach((timestamp, key) => {
      if (key.startsWith(`${plantId}_`)) {
        keysToDelete.push(key);
      }
    });
    
    keysToDelete.forEach(key => {
      this.alertHistory.delete(key);
    });
    
    console.log(`PlantAlertService: Cleared alert history for plant ${plantId}`);
  }

  /**
   * Update monitoring when settings change
   */
  async updateMonitoring(userId, userSettings) {
    if (userSettings?.alertNotificationsEnabled) {
      if (!this.isMonitoring) {
        await this.startMonitoring(userId, userSettings);
      }
    } else {
      if (this.isMonitoring) {
        this.stopMonitoring();
      }
    }
  }
}

// Export singleton instance
export default new PlantAlertService();

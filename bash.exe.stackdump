Stack trace:
Frame         Function      Args
0007FFFFABF0  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFABF0, 0007FFFF9AF0) msys-2.0.dll+0x1FEBA
0007FFFFABF0  0002100467F9 (000000000000, 000000000000, 000210059F9C, 0007FFFFAEC8) msys-2.0.dll+0x67F9
0007FFFFABF0  000210046832 (0002102860A8, 0007FFFFAAA8, 0007FFFFABF0, 0002102684E0) msys-2.0.dll+0x6832
0007FFFFABF0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFABF0  00021006918F (0007FFFFAC00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2918F
0007FFFFAED0  00021006A49D (000000004000, 000000000000, 000000000008, 000000000008) msys-2.0.dll+0x2A49D
0007FFFFAED0  00021006A719 (000000000008, 0007FFFFAF78, 0007FFFFB154, 0000FFFFFFFF) msys-2.0.dll+0x2A719
0007FFFFAED0  0002101948DB (000000000008, 0007FFFFAF78, 0007FFFFB154, 0000FFFFFFFF) msys-2.0.dll+0x1548DB
0007FFFFAED0  00010042DB65 (000000000004, 0007FFFFB154, 0002101948DB, 000210268720) bash.exe+0x2DB65
0000FFFFFFFF  00010043C4F8 (0000000000C2, 000000000000, 000A00102B20, 000A0008B7E0) bash.exe+0x3C4F8
000000000070  00010043E6BE (000000000018, 000A00000001, 000210179682, 0007FFFFB160) bash.exe+0x3E6BE
000000000070  000100441B06 (000700000001, 000200000000, 0007FFFFB250, 000000000000) bash.exe+0x41B06
000000000070  000100441D36 (000A00000000, 000000000000, 000000000000, 000000000000) bash.exe+0x41D36
000000000051  0001004449D8 (000000000000, 000000000010, 000210268720, 000A0004A870) bash.exe+0x449D8
000000000051  00010043D697 (000000000010, 000000000000, 0002101948DB, 000A0009A060) bash.exe+0x3D697
000000000010  00010043DC63 (000000000001, 000000000001, 000A00098EF0, 000A0009B680) bash.exe+0x3DC63
000000000000  000100433786 (000000000001, 000000000000, 00010061F274, 000A001CB860) bash.exe+0x33786
000000000001  0001004440DE (000200000000, 000100000000, 0002FFFFFFFF, 000000000000) bash.exe+0x440DE
0000FFFFFFFF  000100419C92 (0001004358D4, 000000000000, 000000000010, 000A0004D320) bash.exe+0x19C92
000A0004D320  00010041C54A (00010048F6F5, 000210268720, 000100620700, 000A0004D320) bash.exe+0x1C54A
0000FFFFFFFF  0001004179C7 (0002101948DB, 0000FFFFFFFF, 0001004F70ED, 000A0004D320) bash.exe+0x179C7
000A0009B490  00010041AC6A (00021017972F, 000000000000, 0001004EB4A0, 000100000000) bash.exe+0x1AC6A
000A0009B490  00010041C50F (000200000000, 000000000000, 000210179600, 000A0009B490) bash.exe+0x1C50F
0000FFFFFFFF  0001004179C7 (0001004F70ED, 000000000000, 00010044D0CA, 000A0009B490) bash.exe+0x179C7
000000000160  00010041AC6A (000200000000, 000100000000, 000210179600, 000000000000) bash.exe+0x1AC6A
000000000160  00010041813E (000210179682, 000000000000, 000210268720, 000A0009B220) bash.exe+0x1813E
000A0009B220  00010041C54A (000A001CB190, 000210268720, 000100620700, 000A0009B220) bash.exe+0x1C54A
0000FFFFFFFF  0001004179C7 (000A00024350, 000A001AD3F0, 0001004F70ED, 000A0009B220) bash.exe+0x179C7
000A0009B300  00010041AC6A (000A001AD360, 000210268720, DFDFDFDFDFDFDFDF, 0001004F710E) bash.exe+0x1AC6A
000A0009B300  00010041C50F (00000000001F, 0000001A0018, 0007FFFFC0A0, 000A0009B300) bash.exe+0x1C50F
0000FFFFFFFF  0001004179C7 (0001004F70ED, 000000000000, 00010044D0CA, 000A0009B300) bash.exe+0x179C7
000000000160  00010041AC6A (000210179682, 000A00000000, 0001004261D4, 000A0009B570) bash.exe+0x1AC6A
End of stack trace (more stack frames may be present)
Loaded modules:
000100400000 bash.exe
7FF8FBAD0000 ntdll.dll
7FF8FB040000 KERNEL32.DLL
7FF8F8D10000 KERNELBASE.dll
7FF8F9BD0000 USER32.dll
7FF8F8CE0000 win32u.dll
000210040000 msys-2.0.dll
7FF8FB4A0000 GDI32.dll
7FF8F95B0000 gdi32full.dll
7FF8F9450000 msvcp_win.dll
7FF8F8BC0000 ucrtbase.dll
7FF8F9910000 advapi32.dll
7FF8FAF30000 msvcrt.dll
7FF8FA5B0000 sechost.dll
7FF8F90F0000 bcrypt.dll
7FF8F97D0000 RPCRT4.dll
7FF8F8220000 CRYPTBASE.DLL
7FF8F96E0000 bcryptPrimitives.dll
7FF8FAFE0000 IMM32.DLL

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import { COLORS, SIZES, FONTS, SHADOWS } from '../../constants/theme';
import { useAuth } from '../../context/AuthContext';
import PlantImage from '../../components/PlantImage';
import Button from '../../components/Button';
import { getAvailablePots, addPlant } from '../../services/databaseService';

const AddPlantScreen = ({ navigation }) => {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [availablePots, setAvailablePots] = useState([]);
  const [showPotDropdown, setShowPotDropdown] = useState(false);

  // Plant details state
  const [plantData, setPlantData] = useState({
    name: '',
    type: '',
    description: '',
    potId: '',
    imageBase64: null,
    conditions: {
      soilMoisture: {
        min: 30,
        max: 70,
      },
      temperature: {
        min: 18,
        max: 28,
      },
      humidity: {
        min: 40,
        max: 80,
      },
    },
  });

  // Load available pots on component mount
  useEffect(() => {
    loadAvailablePots();
  }, []);

  const loadAvailablePots = async () => {
    try {
      const pots = await getAvailablePots();
      setAvailablePots(pots);
    } catch (error) {
      console.error('Error loading available pots:', error);
      Alert.alert('Error', 'Failed to load available pots');
    }
  };

  const handleInputChange = (field, value) => {
    setPlantData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleConditionChange = (condition, type, value) => {
    const numValue = parseInt(value) || 0;
    setPlantData(prev => ({
      ...prev,
      conditions: {
        ...prev.conditions,
        [condition]: {
          ...prev.conditions[condition],
          [type]: numValue,
        },
      },
    }));
  };

  const handleImageChange = (imageData) => {
    if (imageData) {
      // Extract base64 from data URL
      const base64 = imageData.split(',')[1];
      setPlantData(prev => ({
        ...prev,
        imageBase64: base64,
      }));
    } else {
      setPlantData(prev => ({
        ...prev,
        imageBase64: null,
      }));
    }
  };

  const validateForm = () => {
    if (!plantData.name.trim()) {
      Alert.alert('Validation Error', 'Please enter a plant name');
      return false;
    }
    if (!plantData.type.trim()) {
      Alert.alert('Validation Error', 'Please enter a plant type');
      return false;
    }
    if (!plantData.potId) {
      Alert.alert('Validation Error', 'Please select a pot for your plant');
      return false;
    }
    return true;
  };

  const handleNext = () => {
    if (!validateForm()) return;

    // Navigate to conditions setup screen
    navigation.navigate('PlantConditions', {
      plantData,
      onSave: handleSavePlant,
    });
  };

  const handleSavePlant = async (finalPlantData) => {
    setLoading(true);
    try {
      await addPlant(currentUser.uid, finalPlantData);
      Alert.alert(
        'Success',
        'Plant added successfully!',
        [
          {
            text: 'OK',
            onPress: () => navigation.navigate('MainTabs', { screen: 'Home' }),
          },
        ]
      );
    } catch (error) {
      console.error('Error adding plant:', error);
      Alert.alert('Error', 'Failed to add plant. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getSelectedPotName = () => {
    const selectedPot = availablePots.find(pot => pot.id === plantData.potId);
    return selectedPot ? selectedPot.name : 'Select a pot';
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />

      <LinearGradient
        colors={COLORS.gradientPrimary}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.headerGradient}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-left" size={SIZES.iconLarge} color={COLORS.white} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Add New Plant</Text>
          <View style={styles.headerSpacer} />
        </View>
      </LinearGradient>

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Plant Image Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Plant Photo</Text>
          <View style={styles.imageSection}>
            <PlantImage
              plantId={`new-plant-${Date.now()}`}
              plantType={plantData.type || 'default'}
              size="large"
              editable={true}
              onImageChange={handleImageChange}
              style={styles.plantImage}
            />
            <Text style={styles.imageHint}>
              Tap to add a photo of your plant
            </Text>
          </View>
        </View>

        {/* Basic Details Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Plant Details</Text>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Plant Name *</Text>
            <TextInput
              style={styles.textInput}
              value={plantData.name}
              onChangeText={(value) => handleInputChange('name', value)}
              placeholder="e.g., My Tomato Plant"
              placeholderTextColor={COLORS.gray500}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Plant Type *</Text>
            <TextInput
              style={styles.textInput}
              value={plantData.type}
              onChangeText={(value) => handleInputChange('type', value)}
              placeholder="e.g., Tomato, Basil, Succulent"
              placeholderTextColor={COLORS.gray500}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Description (Optional)</Text>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              value={plantData.description}
              onChangeText={(value) => handleInputChange('description', value)}
              placeholder="Add notes about your plant..."
              placeholderTextColor={COLORS.gray500}
              multiline
              numberOfLines={3}
            />
          </View>
        </View>

        {/* Pot Assignment Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Pot Assignment</Text>
          <Text style={styles.sectionDescription}>
            Choose which pot your plant will be placed in
          </Text>

          <TouchableOpacity
            style={styles.dropdown}
            onPress={() => setShowPotDropdown(!showPotDropdown)}
          >
            <Text style={[
              styles.dropdownText,
              !plantData.potId && styles.dropdownPlaceholder
            ]}>
              {getSelectedPotName()}
            </Text>
            <Icon
              name={showPotDropdown ? "chevron-up" : "chevron-down"}
              size={SIZES.iconMedium}
              color={COLORS.gray600}
            />
          </TouchableOpacity>

          {showPotDropdown && (
            <View style={styles.dropdownList}>
              {availablePots.filter(pot => pot.available).map((pot) => (
                <TouchableOpacity
                  key={pot.id}
                  style={[
                    styles.dropdownItem,
                    plantData.potId === pot.id && styles.dropdownItemSelected
                  ]}
                  onPress={() => {
                    handleInputChange('potId', pot.id);
                    setShowPotDropdown(false);
                  }}
                >
                  <View style={styles.potInfo}>
                    <Text style={styles.potName}>{pot.name}</Text>
                    <Text style={styles.potStatus}>
                      Status: Available
                    </Text>
                  </View>
                  <Icon name="check-circle" size={SIZES.iconMedium} color={COLORS.success} />
                </TouchableOpacity>
              ))}
              {availablePots.filter(pot => pot.available).length === 0 && (
                <View style={styles.noPots}>
                  <Text style={styles.noPotsText}>No available pots</Text>
                  <Text style={styles.noPotsSubtext}>
                    All pots are currently occupied by other plants
                  </Text>
                </View>
              )}
            </View>
          )}
        </View>

        {/* Next Button */}
        <Button
          title="Next: Set Conditions"
          onPress={handleNext}
          loading={loading}
          style={styles.saveButton}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  headerGradient: {
    paddingBottom: SIZES.paddingMedium,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SIZES.paddingLarge,
    paddingTop: SIZES.paddingLarge,
    paddingBottom: SIZES.paddingMedium,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: FONTS.h3,
    fontWeight: '700',
    color: COLORS.white,
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40,
  },
  scrollContent: {
    paddingHorizontal: SIZES.paddingLarge,
    paddingBottom: SIZES.paddingLarge,
  },
  section: {
    marginBottom: SIZES.marginLarge,
  },
  sectionTitle: {
    fontSize: FONTS.h4,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SIZES.marginSmall,
  },
  sectionDescription: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    marginBottom: SIZES.marginMedium,
  },
  imageSection: {
    alignItems: 'center',
    paddingVertical: SIZES.paddingLarge,
  },
  plantImage: {
    marginBottom: SIZES.marginMedium,
  },
  imageHint: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: SIZES.marginMedium,
  },
  inputLabel: {
    fontSize: FONTS.body2,
    fontWeight: '500',
    color: COLORS.textPrimary,
    marginBottom: SIZES.marginSmall,
  },
  textInput: {
    borderWidth: 1,
    borderColor: COLORS.gray300,
    borderRadius: SIZES.radiusMedium,
    paddingHorizontal: SIZES.paddingMedium,
    paddingVertical: SIZES.paddingSmall,
    fontSize: FONTS.body2,
    color: COLORS.textPrimary,
    backgroundColor: COLORS.white,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  dropdown: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: COLORS.gray300,
    borderRadius: SIZES.radiusMedium,
    paddingHorizontal: SIZES.paddingMedium,
    paddingVertical: SIZES.paddingSmall,
    backgroundColor: COLORS.white,
  },
  dropdownText: {
    fontSize: FONTS.body2,
    color: COLORS.textPrimary,
    flex: 1,
  },
  dropdownPlaceholder: {
    color: COLORS.gray500,
  },
  dropdownList: {
    borderWidth: 1,
    borderColor: COLORS.gray300,
    borderRadius: SIZES.radiusMedium,
    backgroundColor: COLORS.white,
    marginTop: SIZES.marginSmall,
    ...SHADOWS.medium,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SIZES.paddingMedium,
    paddingVertical: SIZES.paddingSmall,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray200,
  },
  dropdownItemSelected: {
    backgroundColor: COLORS.primaryLightest,
  },
  potInfo: {
    flex: 1,
  },
  potName: {
    fontSize: FONTS.body2,
    fontWeight: '500',
    color: COLORS.textPrimary,
  },
  potStatus: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    marginTop: 2,
  },
  saveButton: {
    marginTop: SIZES.marginLarge,
  },
  noPots: {
    padding: SIZES.paddingLarge,
    alignItems: 'center',
  },
  noPotsText: {
    fontSize: FONTS.body2,
    fontWeight: '500',
    color: COLORS.textPrimary,
    marginBottom: SIZES.marginXS,
  },
  noPotsSubtext: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
});

export default AddPlantScreen;

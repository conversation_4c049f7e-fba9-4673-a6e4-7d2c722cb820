import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  updateProfile,
  onAuthStateChanged,
  updatePassword,
  EmailAuthProvider,
  reauthenticateWithCredential
} from 'firebase/auth';
import { getAuthInstance } from './firebase';
import { updateUserProfile } from './databaseService';

/**
 * Register a new user with email and password
 * @param {string} email - User's email
 * @param {string} password - User's password
 * @param {string} displayName - User's display name
 * @returns {Promise} - Promise that resolves with user credential
 */
export const registerUser = async (email, password, displayName) => {
  try {
    const authInstance = await getAuthInstance();
    if (!authInstance) {
      throw new Error('Auth is not initialized');
    }
    // Create user with email and password
    const userCredential = await createUserWithEmailAndPassword(authInstance, email, password);

    // Update user profile with display name
    await updateProfile(userCredential.user, {
      displayName: displayName
    });

    // Create user document in Firestore
    await updateUserProfile(userCredential.user.uid, {
      email,
      displayName,
      createdAt: new Date().toISOString(),
      lastLogin: new Date().toISOString(),
    });

    return userCredential;
  } catch (error) {
    throw error;
  }
};

/**
 * Sign in an existing user with email and password
 * @param {string} email - User's email
 * @param {string} password - User's password
 * @returns {Promise} - Promise that resolves with user credential
 */
export const loginUser = async (email, password) => {
  try {
    const authInstance = await getAuthInstance();
    if (!authInstance) {
      throw new Error('Auth is not initialized');
    }
    const userCredential = await signInWithEmailAndPassword(authInstance, email, password);

    // Update last login timestamp
    await updateUserProfile(userCredential.user.uid, {
      lastLogin: new Date().toISOString(),
    });

    return userCredential;
  } catch (error) {
    throw error;
  }
};

/**
 * Sign out the current user
 * @returns {Promise} - Promise that resolves when sign out is complete
 */
export const logoutUser = async () => {
  try {
    const authInstance = await getAuthInstance();
    return await signOut(authInstance);
  } catch (error) {
    throw error;
  }
};

/**
 * Send password reset email
 * @param {string} email - User's email
 * @returns {Promise} - Promise that resolves when email is sent
 */
export const resetPassword = async (email) => {
  try {
    const authInstance = await getAuthInstance();
    return await sendPasswordResetEmail(authInstance, email);
  } catch (error) {
    throw error;
  }
};

/**
 * Update user's password
 * @param {string} currentPassword - User's current password
 * @param {string} newPassword - User's new password
 * @returns {Promise} - Promise that resolves when password is updated
 */
export const changePassword = async (currentPassword, newPassword) => {
  try {
    const authInstance = await getAuthInstance();
    const user = authInstance.currentUser;

    if (!user) {
      throw new Error('No user is currently signed in');
    }

    // Re-authenticate user before changing password
    const credential = EmailAuthProvider.credential(user.email, currentPassword);
    await reauthenticateWithCredential(user, credential);

    // Update password
    return await updatePassword(user, newPassword);
  } catch (error) {
    throw error;
  }
};

/**
 * Get the current authenticated user
 * @returns {Object|null} - Current user or null if not authenticated
 */
export const getCurrentUser = async () => {
  try {
    const authInstance = await getAuthInstance();
    if (!authInstance) {
      console.error('Auth is not initialized');
      return null;
    }
    return authInstance.currentUser;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
};

/**
 * Subscribe to auth state changes
 * @param {function} callback - Callback function to handle auth state changes
 * @returns {function} - Unsubscribe function
 */
export const subscribeAuthState = async (callback) => {
  try {
    const authInstance = await getAuthInstance();
    if (!authInstance) {
      console.error('Auth is not initialized');
      return () => {}; // Return empty unsubscribe function
    }
    return onAuthStateChanged(authInstance, callback);
  } catch (error) {
    console.error('Error subscribing to auth state:', error);
    return () => {}; // Return empty unsubscribe function
  }
};

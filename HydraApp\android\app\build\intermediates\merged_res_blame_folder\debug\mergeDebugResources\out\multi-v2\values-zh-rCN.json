{"logs": [{"outputFile": "com.hydraiot.app-mergeDebugResources-51:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0a1c3fd62b5fb636be47a17086a4f32c\\transformed\\play-services-basement-18.5.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "94", "endOffsets": "293"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5028", "endColumns": "98", "endOffsets": "5122"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd1664c1c7ce01ff711fc15460fc5485\\transformed\\credentials-1.2.0-rc01\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,110", "endOffsets": "156,267"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "2875,2981", "endColumns": "105,110", "endOffsets": "2976,3087"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79f6ab95733be72bb6af37d95e29f537\\transformed\\browser-1.4.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "66,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "5980,6202,6294,6395", "endColumns": "82,91,100,92", "endOffsets": "6058,6289,6390,6483"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa0e231f4d27dc8a031f0119595b6f1\\transformed\\material-1.6.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,272,363,469,542,604,681,740,799,877,934,990,1049,1107,1161,1246,1302,1360,1414,1479,1571,1645,1721,1813,1875,1937,2016,2083,2149,2213,2282,2360,2421,2492,2559,2619,2698,2765,2848,2933,3007,3072,3148,3196,3260,3336,3414,3476,3540,3603,3683,3759,3837,3914", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,69,90,105,72,61,76,58,58,77,56,55,58,57,53,84,55,57,53,64,91,73,75,91,61,61,78,66,65,63,68,77,60,70,66,59,78,66,82,84,73,64,75,47,63,75,77,61,63,62,79,75,77,76,68", "endOffsets": "197,267,358,464,537,599,676,735,794,872,929,985,1044,1102,1156,1241,1297,1355,1409,1474,1566,1640,1716,1808,1870,1932,2011,2078,2144,2208,2277,2355,2416,2487,2554,2614,2693,2760,2843,2928,3002,3067,3143,3191,3255,3331,3409,3471,3535,3598,3678,3754,3832,3909,3978"}, "to": {"startLines": "2,36,44,45,46,67,68,73,76,78,79,80,81,82,83,84,85,86,87,88,89,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3092,3826,3917,4023,6063,6125,6555,6755,6879,6957,7014,7070,7129,7187,7241,7326,7382,7440,7494,7559,7856,7930,8006,8098,8160,8222,8301,8368,8434,8498,8567,8645,8706,8777,8844,8904,8983,9050,9133,9218,9292,9357,9433,9481,9545,9621,9699,9761,9825,9888,9968,10044,10122,10199", "endLines": "5,36,44,45,46,67,68,73,76,78,79,80,81,82,83,84,85,86,87,88,89,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126", "endColumns": "12,69,90,105,72,61,76,58,58,77,56,55,58,57,53,84,55,57,53,64,91,73,75,91,61,61,78,66,65,63,68,77,60,70,66,59,78,66,82,84,73,64,75,47,63,75,77,61,63,62,79,75,77,76,68", "endOffsets": "247,3157,3912,4018,4091,6120,6197,6609,6809,6952,7009,7065,7124,7182,7236,7321,7377,7435,7489,7554,7646,7925,8001,8093,8155,8217,8296,8363,8429,8493,8562,8640,8701,8772,8839,8899,8978,9045,9128,9213,9287,9352,9428,9476,9540,9616,9694,9756,9820,9883,9963,10039,10117,10194,10263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ff5b0ce559ae890269420b3dec0e4060\\transformed\\react-android-0.79.2-debug\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,191,258,324,399,464,529,598,669,742,814,882,953,1026,1098,1175,1251,1323,1393,1462,1540,1608,1679,1746", "endColumns": "65,69,66,65,74,64,64,68,70,72,71,67,70,72,71,76,75,71,69,68,77,67,70,66,68", "endOffsets": "116,186,253,319,394,459,524,593,664,737,809,877,948,1021,1093,1170,1246,1318,1388,1457,1535,1603,1674,1741,1810"}, "to": {"startLines": "33,47,72,74,75,77,90,91,92,127,128,129,130,132,133,134,135,136,137,138,139,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2809,4096,6488,6614,6680,6814,7651,7716,7785,10268,10341,10413,10481,10631,10704,10776,10853,10929,11001,11071,11140,11319,11387,11458,11525", "endColumns": "65,69,66,65,74,64,64,68,70,72,71,67,70,72,71,76,75,71,69,68,77,67,70,66,68", "endOffsets": "2870,4161,6550,6675,6750,6874,7711,7780,7851,10336,10408,10476,10547,10699,10771,10848,10924,10996,11066,11135,11213,11382,11453,11520,11589"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7312d9ce0e64b42e563cee99ce0c7cbc\\transformed\\play-services-base-18.5.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,419,530,628,729,841,939,1027,1131,1228,1354,1465,1565,1669,1721,1774", "endColumns": "96,124,110,97,100,111,97,87,103,96,125,110,99,103,51,52,69", "endOffsets": "293,418,529,627,728,840,938,1026,1130,1227,1353,1464,1564,1668,1720,1773,1843"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4166,4267,4396,4511,4613,4718,4834,4936,5127,5235,5336,5466,5581,5685,5793,5849,5906", "endColumns": "100,128,114,101,104,115,101,91,107,100,129,114,103,107,55,56,73", "endOffsets": "4262,4391,4506,4608,4713,4829,4931,5023,5230,5331,5461,5576,5680,5788,5844,5901,5975"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af63e50a53e992ea9c751fc2deb2fc57\\transformed\\appcompat-1.7.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,131", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "252,347,442,542,624,721,827,904,979,1070,1163,1260,1356,1450,1543,1638,1730,1821,1912,1990,2086,2181,2276,2373,2469,2567,2715,10552", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "342,437,537,619,716,822,899,974,1065,1158,1255,1351,1445,1538,1633,1725,1816,1907,1985,2081,2176,2271,2368,2464,2562,2710,2804,10626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3135f157d17fbe3966fe84b667792db9\\transformed\\core-1.13.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "37,38,39,40,41,42,43,140", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3162,3254,3355,3449,3543,3636,3730,11218", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "3249,3350,3444,3538,3631,3725,3821,11314"}}]}]}
# C/C++ build system timings
generate_cxx_metadata
  [gap of 73ms]
  create-invalidation-state 103ms
  generate-prefab-packages
    [gap of 71ms]
    exec-prefab 4733ms
    [gap of 70ms]
  generate-prefab-packages completed in 4874ms
  execute-generate-process
    exec-configure 1675ms
    [gap of 989ms]
  execute-generate-process completed in 2666ms
  [gap of 161ms]
  write-metadata-json-to-file 11ms
generate_cxx_metadata completed in 7895ms


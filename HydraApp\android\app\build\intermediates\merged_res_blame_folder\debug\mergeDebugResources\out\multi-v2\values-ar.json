{"logs": [{"outputFile": "com.hydraiot.app-mergeDebugResources-51:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa0e231f4d27dc8a031f0119595b6f1\\transformed\\material-1.6.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,420,504,605,724,801,864,955,1024,1091,1191,1256,1317,1385,1447,1505,1619,1679,1740,1797,1870,1993,2074,2154,2272,2353,2434,2523,2590,2656,2734,2814,2898,2970,3044,3117,3187,3278,3349,3439,3534,3608,3691,3784,3833,3902,3988,4073,4135,4199,4262,4371,4463,4560,4653", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "12,83,100,118,76,62,90,68,66,99,64,60,67,61,57,113,59,60,56,72,122,80,79,117,80,80,88,66,65,77,79,83,71,73,72,69,90,70,89,94,73,82,92,48,68,85,84,61,63,62,108,91,96,92,79", "endOffsets": "415,499,600,719,796,859,950,1019,1086,1186,1251,1312,1380,1442,1500,1614,1674,1735,1792,1865,1988,2069,2149,2267,2348,2429,2518,2585,2651,2729,2809,2893,2965,3039,3112,3182,3273,3344,3434,3529,3603,3686,3779,3828,3897,3983,4068,4130,4194,4257,4366,4458,4555,4648,4728"}, "to": {"startLines": "2,40,48,49,50,71,72,77,80,82,83,84,85,86,87,88,89,90,91,92,93,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3427,4223,4324,4443,6798,6861,7332,7547,7681,7781,7846,7907,7975,8037,8095,8209,8269,8330,8387,8460,8810,8891,8971,9089,9170,9251,9340,9407,9473,9551,9631,9715,9787,9861,9934,10004,10095,10166,10256,10351,10425,10508,10601,10650,10719,10805,10890,10952,11016,11079,11188,11280,11377,11470", "endLines": "9,40,48,49,50,71,72,77,80,82,83,84,85,86,87,88,89,90,91,92,93,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "endColumns": "12,83,100,118,76,62,90,68,66,99,64,60,67,61,57,113,59,60,56,72,122,80,79,117,80,80,88,66,65,77,79,83,71,73,72,69,90,70,89,94,73,82,92,48,68,85,84,61,63,62,108,91,96,92,79", "endOffsets": "465,3506,4319,4438,4515,6856,6947,7396,7609,7776,7841,7902,7970,8032,8090,8204,8264,8325,8382,8455,8578,8886,8966,9084,9165,9246,9335,9402,9468,9546,9626,9710,9782,9856,9929,9999,10090,10161,10251,10346,10420,10503,10596,10645,10714,10800,10885,10947,11011,11074,11183,11275,11372,11465,11545"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ff5b0ce559ae890269420b3dec0e4060\\transformed\\react-android-0.79.2-debug\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,206,278,346,424,491,561,639,718,799,887,965,1045,1129,1203,1281,1358,1433,1512,1584,1668,1738,1824,1893", "endColumns": "68,81,71,67,77,66,69,77,78,80,87,77,79,83,73,77,76,74,78,71,83,69,85,68,77", "endOffsets": "119,201,273,341,419,486,556,634,713,794,882,960,1040,1124,1198,1276,1353,1428,1507,1579,1663,1733,1819,1888,1966"}, "to": {"startLines": "37,51,76,78,79,81,94,95,96,131,132,133,134,136,137,138,139,140,141,142,143,145,146,147,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3124,4520,7260,7401,7469,7614,8583,8653,8731,11550,11631,11719,11797,11959,12043,12117,12195,12272,12347,12426,12498,12683,12753,12839,12908", "endColumns": "68,81,71,67,77,66,69,77,78,80,87,77,79,83,73,77,76,74,78,71,83,69,85,68,77", "endOffsets": "3188,4597,7327,7464,7542,7676,8648,8726,8805,11626,11714,11792,11872,12038,12112,12190,12267,12342,12421,12493,12577,12748,12834,12903,12981"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7312d9ce0e64b42e563cee99ce0c7cbc\\transformed\\play-services-base-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,433,551,652,786,910,1017,1115,1248,1348,1494,1612,1747,1889,1949,2011", "endColumns": "99,139,117,100,133,123,106,97,132,99,145,117,134,141,59,61,79", "endOffsets": "292,432,550,651,785,909,1016,1114,1247,1347,1493,1611,1746,1888,1948,2010,2090"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4602,4706,4850,4972,5077,5215,5343,5454,5686,5823,5927,6077,6199,6338,6484,6548,6614", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "4701,4845,4967,5072,5210,5338,5449,5551,5818,5922,6072,6194,6333,6479,6543,6609,6693"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af63e50a53e992ea9c751fc2deb2fc57\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "470,578,682,789,871,972,1086,1166,1245,1336,1429,1521,1615,1715,1808,1903,1996,2087,2181,2260,2365,2463,2561,2669,2769,2872,3027,11877", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "573,677,784,866,967,1081,1161,1240,1331,1424,1516,1610,1710,1803,1898,1991,2082,2176,2255,2360,2458,2556,2664,2764,2867,3022,3119,11954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3135f157d17fbe3966fe84b667792db9\\transformed\\core-1.13.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "41,42,43,44,45,46,47,144", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3511,3604,3706,3801,3904,4007,4109,12582", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "3599,3701,3796,3899,4002,4104,4218,12678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0a1c3fd62b5fb636be47a17086a4f32c\\transformed\\play-services-basement-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5556", "endColumns": "129", "endOffsets": "5681"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd1664c1c7ce01ff711fc15460fc5485\\transformed\\credentials-1.2.0-rc01\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,118", "endOffsets": "165,284"}, "to": {"startLines": "38,39", "startColumns": "4,4", "startOffsets": "3193,3308", "endColumns": "114,118", "endOffsets": "3303,3422"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79f6ab95733be72bb6af37d95e29f537\\transformed\\browser-1.4.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,101", "endOffsets": "150,248,356,458"}, "to": {"startLines": "70,73,74,75", "startColumns": "4,4,4,4", "startOffsets": "6698,6952,7050,7158", "endColumns": "99,97,107,101", "endOffsets": "6793,7045,7153,7255"}}]}]}
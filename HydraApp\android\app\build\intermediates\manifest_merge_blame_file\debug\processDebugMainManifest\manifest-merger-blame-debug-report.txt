1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.hydraiot.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:4:5-67
11-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.CAMERA" />
12-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:5:5-65
12-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:5:22-62
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:6:5-80
13-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:6:22-77
14    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
14-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:7:5-81
14-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:7:22-78
15    <uses-permission android:name="android.permission.WAKE_LOCK" />
15-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
15-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
16-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-76
17    <!--
18    This manifest file is used only by Gradle to configure debug-only capabilities
19    for React Native Apps.
20    -->
21    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
21-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff5b0ce559ae890269420b3dec0e4060\transformed\react-android-0.79.2-debug\AndroidManifest.xml:16:5-78
21-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff5b0ce559ae890269420b3dec0e4060\transformed\react-android-0.79.2-debug\AndroidManifest.xml:16:22-75
22    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
22-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
22-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:23:22-74
23    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
23-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
23-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
24    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
24-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
24-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:25:22-76
25    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
25-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
25-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
26    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
26-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
26-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
27    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
27-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
27-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
28    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
28-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9df795276c866ca4d04584fbdec824f3\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
28-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9df795276c866ca4d04584fbdec824f3\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
29
30    <permission
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3135f157d17fbe3966fe84b667792db9\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
31        android:name="com.hydraiot.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
31-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3135f157d17fbe3966fe84b667792db9\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
32        android:protectionLevel="signature" />
32-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3135f157d17fbe3966fe84b667792db9\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
33
34    <uses-permission android:name="com.hydraiot.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3135f157d17fbe3966fe84b667792db9\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3135f157d17fbe3966fe84b667792db9\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
35
36    <application
36-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:9:5-29:19
37        android:name="com.hydraiot.app.MainApplication"
37-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:10:7-38
38        android:allowBackup="false"
38-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:14:7-34
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3135f157d17fbe3966fe84b667792db9\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
40        android:debuggable="true"
41        android:extractNativeLibs="false"
42        android:icon="@mipmap/ic_launcher"
42-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:12:7-41
43        android:label="@string/app_name"
43-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:11:7-39
44        android:roundIcon="@mipmap/ic_launcher_round"
44-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:13:7-52
45        android:supportsRtl="true"
45-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:16:7-33
46        android:theme="@style/AppTheme"
46-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:15:7-38
47        android:usesCleartextTraffic="true" >
47-->E:\HYDRA-IOT\HydraApp\android\app\src\debug\AndroidManifest.xml:6:9-44
48        <activity
48-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:17:7-28:18
49            android:name="com.hydraiot.app.MainActivity"
49-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:18:9-37
50            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
50-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:20:9-118
51            android:exported="true"
51-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:23:9-32
52            android:label="@string/app_name"
52-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:19:9-41
53            android:launchMode="singleTask"
53-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:21:9-40
54            android:windowSoftInputMode="adjustResize" >
54-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:22:9-51
55            <intent-filter>
55-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:24:9-27:25
56                <action android:name="android.intent.action.MAIN" />
56-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:25:13-65
56-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:25:21-62
57
58                <category android:name="android.intent.category.LAUNCHER" />
58-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:26:13-73
58-->E:\HYDRA-IOT\HydraApp\android\app\src\main\AndroidManifest.xml:26:23-70
59            </intent-filter>
60        </activity>
61
62        <service
62-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:40
63            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingHeadlessService"
63-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-103
64            android:exported="false" />
64-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-37
65        <service
65-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-21:19
66            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingService"
66-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-95
67            android:exported="false" >
67-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
68            <intent-filter>
68-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:29
69                <action android:name="com.google.firebase.MESSAGING_EVENT" />
69-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-78
69-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-75
70            </intent-filter>
71        </service>
72
73        <receiver
73-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-30:20
74            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingReceiver"
74-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-96
75            android:exported="true"
75-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-36
76            android:permission="com.google.android.c2dm.permission.SEND" >
76-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-73
77            <intent-filter>
77-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
78                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
78-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
78-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
79            </intent-filter>
80        </receiver>
81
82        <meta-data
82-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-34:37
83            android:name="delivery_metrics_exported_to_big_query_enabled"
83-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-74
84            android:value="false" />
84-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-34
85        <meta-data
85-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-37:36
86            android:name="firebase_messaging_auto_init_enabled"
86-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-64
87            android:value="true" />
87-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-33
88        <meta-data
88-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-40:37
89            android:name="firebase_messaging_notification_delegation_enabled"
89-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-78
90            android:value="false" />
90-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-34
91        <meta-data
91-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-43:32
92            android:name="com.google.firebase.messaging.default_notification_channel_id"
92-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-89
93            android:value="" />
93-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-29
94        <meta-data
94-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:9-46:47
95            android:name="com.google.firebase.messaging.default_notification_color"
95-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-84
96            android:resource="@color/white" />
96-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:13-44
97        <meta-data
97-->[:react-native-firebase_app] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:36
98            android:name="app_data_collection_default_enabled"
98-->[:react-native-firebase_app] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-63
99            android:value="true" />
99-->[:react-native-firebase_app] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-33
100
101        <service
101-->[:react-native-firebase_app] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:19
102            android:name="com.google.firebase.components.ComponentDiscoveryService"
102-->[:react-native-firebase_app] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-84
103            android:directBootAware="true"
103-->[:react-native-firebase_app] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-43
104            android:exported="false" >
104-->[:react-native-firebase_app] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
105            <meta-data
105-->[:react-native-firebase_app] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:85
106                android:name="com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar"
106-->[:react-native-firebase_app] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-120
107                android:value="com.google.firebase.components.ComponentRegistrar" />
107-->[:react-native-firebase_app] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-82
108            <meta-data
108-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:69:13-71:85
109                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
109-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:70:17-109
110                android:value="com.google.firebase.components.ComponentRegistrar" />
110-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:71:17-82
111            <meta-data
111-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3156559cff9133f8a8276d65cce1efb3\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
112                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
112-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3156559cff9133f8a8276d65cce1efb3\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
113                android:value="com.google.firebase.components.ComponentRegistrar" />
113-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3156559cff9133f8a8276d65cce1efb3\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
114            <meta-data
114-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3156559cff9133f8a8276d65cce1efb3\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
115                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
115-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3156559cff9133f8a8276d65cce1efb3\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
116                android:value="com.google.firebase.components.ComponentRegistrar" />
116-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3156559cff9133f8a8276d65cce1efb3\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
117            <meta-data
117-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\606023a00c045cfbe5a1c6257dd0c4f9\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
118                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
118-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\606023a00c045cfbe5a1c6257dd0c4f9\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
119                android:value="com.google.firebase.components.ComponentRegistrar" />
119-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\606023a00c045cfbe5a1c6257dd0c4f9\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
120            <meta-data
120-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\606023a00c045cfbe5a1c6257dd0c4f9\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
121                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
121-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\606023a00c045cfbe5a1c6257dd0c4f9\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
122                android:value="com.google.firebase.components.ComponentRegistrar" />
122-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\606023a00c045cfbe5a1c6257dd0c4f9\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
123            <meta-data
123-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
124                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
124-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
126            <meta-data
126-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
127                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
127-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
128                android:value="com.google.firebase.components.ComponentRegistrar" />
128-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
129            <meta-data
129-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
130                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
130-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
131                android:value="com.google.firebase.components.ComponentRegistrar" />
131-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
132            <meta-data
132-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ab52eeae13198c265894b3b8dc0e4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
133                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
133-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ab52eeae13198c265894b3b8dc0e4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
134                android:value="com.google.firebase.components.ComponentRegistrar" />
134-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ab52eeae13198c265894b3b8dc0e4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
135            <meta-data
135-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ab52eeae13198c265894b3b8dc0e4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
136                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
136-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ab52eeae13198c265894b3b8dc0e4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
137                android:value="com.google.firebase.components.ComponentRegistrar" />
137-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ab52eeae13198c265894b3b8dc0e4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
138            <meta-data
138-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de127579c40774611313ddb57a3bd932\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
139                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
139-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de127579c40774611313ddb57a3bd932\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
140                android:value="com.google.firebase.components.ComponentRegistrar" />
140-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de127579c40774611313ddb57a3bd932\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
141            <meta-data
141-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
142                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
142-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
143                android:value="com.google.firebase.components.ComponentRegistrar" />
143-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
144            <meta-data
144-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3651116cc4ee1cbe9b24817c6e7f6a81\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
145                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
145-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3651116cc4ee1cbe9b24817c6e7f6a81\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
146                android:value="com.google.firebase.components.ComponentRegistrar" />
146-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3651116cc4ee1cbe9b24817c6e7f6a81\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
147        </service>
148
149        <provider
149-->[:react-native-firebase_app] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-27:38
150            android:name="io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider"
150-->[:react-native-firebase_app] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-88
151            android:authorities="com.hydraiot.app.reactnativefirebaseappinitprovider"
151-->[:react-native-firebase_app] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-86
152            android:exported="false"
152-->[:react-native-firebase_app] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-37
153            android:initOrder="99" />
153-->[:react-native-firebase_app] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-35
154        <provider
154-->[:react-native-image-picker] E:\HYDRA-IOT\HydraApp\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
155            android:name="com.imagepicker.ImagePickerProvider"
155-->[:react-native-image-picker] E:\HYDRA-IOT\HydraApp\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-63
156            android:authorities="com.hydraiot.app.imagepickerprovider"
156-->[:react-native-image-picker] E:\HYDRA-IOT\HydraApp\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-71
157            android:exported="false"
157-->[:react-native-image-picker] E:\HYDRA-IOT\HydraApp\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
158            android:grantUriPermissions="true" >
158-->[:react-native-image-picker] E:\HYDRA-IOT\HydraApp\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
159            <meta-data
159-->[:react-native-image-picker] E:\HYDRA-IOT\HydraApp\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:70
160                android:name="android.support.FILE_PROVIDER_PATHS"
160-->[:react-native-image-picker] E:\HYDRA-IOT\HydraApp\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
161                android:resource="@xml/imagepicker_provider_paths" />
161-->[:react-native-image-picker] E:\HYDRA-IOT\HydraApp\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
162        </provider>
163
164        <activity
164-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff5b0ce559ae890269420b3dec0e4060\transformed\react-android-0.79.2-debug\AndroidManifest.xml:19:9-21:40
165            android:name="com.facebook.react.devsupport.DevSettingsActivity"
165-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff5b0ce559ae890269420b3dec0e4060\transformed\react-android-0.79.2-debug\AndroidManifest.xml:20:13-77
166            android:exported="false" />
166-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff5b0ce559ae890269420b3dec0e4060\transformed\react-android-0.79.2-debug\AndroidManifest.xml:21:13-37
167        <activity
167-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:29:9-46:20
168            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
168-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:30:13-80
169            android:excludeFromRecents="true"
169-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:31:13-46
170            android:exported="true"
170-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:32:13-36
171            android:launchMode="singleTask"
171-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:33:13-44
172            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
172-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:34:13-72
173            <intent-filter>
173-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:35:13-45:29
174                <action android:name="android.intent.action.VIEW" />
174-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
174-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
175
176                <category android:name="android.intent.category.DEFAULT" />
176-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
176-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
177                <category android:name="android.intent.category.BROWSABLE" />
177-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
177-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
178
179                <data
179-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:41:17-44:51
180                    android:host="firebase.auth"
180-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:42:21-49
181                    android:path="/"
181-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:43:21-37
182                    android:scheme="genericidp" />
182-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:44:21-48
183            </intent-filter>
184        </activity>
185        <activity
185-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:47:9-64:20
186            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
186-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:48:13-79
187            android:excludeFromRecents="true"
187-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:49:13-46
188            android:exported="true"
188-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:50:13-36
189            android:launchMode="singleTask"
189-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:51:13-44
190            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
190-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:52:13-72
191            <intent-filter>
191-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:53:13-63:29
192                <action android:name="android.intent.action.VIEW" />
192-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
192-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
193
194                <category android:name="android.intent.category.DEFAULT" />
194-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
194-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
195                <category android:name="android.intent.category.BROWSABLE" />
195-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
195-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
196
197                <data
197-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:41:17-44:51
198                    android:host="firebase.auth"
198-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:42:21-49
199                    android:path="/"
199-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:43:21-37
200                    android:scheme="recaptcha" />
200-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f4b33768cf8d601edaf848c418e2de\transformed\firebase-auth-23.2.0\AndroidManifest.xml:44:21-48
201            </intent-filter>
202        </activity>
203
204        <receiver
204-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
205            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
205-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
206            android:exported="true"
206-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
207            android:permission="com.google.android.c2dm.permission.SEND" >
207-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
208            <intent-filter>
208-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
209                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
209-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
209-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
210            </intent-filter>
211
212            <meta-data
212-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
213                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
213-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
214                android:value="true" />
214-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
215        </receiver>
216        <!--
217             FirebaseMessagingService performs security checks at runtime,
218             but set to not exported to explicitly avoid allowing another app to call it.
219        -->
220        <service
220-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
221            android:name="com.google.firebase.messaging.FirebaseMessagingService"
221-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
222            android:directBootAware="true"
222-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
223            android:exported="false" >
223-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
224            <intent-filter android:priority="-500" >
224-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:29
225                <action android:name="com.google.firebase.MESSAGING_EVENT" />
225-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-78
225-->[:react-native-firebase_messaging] E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-75
226            </intent-filter>
227        </service>
228
229        <provider
229-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
230            android:name="com.google.firebase.provider.FirebaseInitProvider"
230-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
231            android:authorities="com.hydraiot.app.firebaseinitprovider"
231-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
232            android:directBootAware="true"
232-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
233            android:exported="false"
233-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
234            android:initOrder="100" />
234-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
235
236        <service
236-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1f27b26140701f2c8d05427d7ea619b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
237            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
237-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1f27b26140701f2c8d05427d7ea619b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
238            android:enabled="true"
238-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1f27b26140701f2c8d05427d7ea619b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
239            android:exported="false" >
239-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1f27b26140701f2c8d05427d7ea619b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
240            <meta-data
240-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1f27b26140701f2c8d05427d7ea619b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
241                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
241-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1f27b26140701f2c8d05427d7ea619b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
242                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
242-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1f27b26140701f2c8d05427d7ea619b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
243        </service>
244
245        <activity
245-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1f27b26140701f2c8d05427d7ea619b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
246            android:name="androidx.credentials.playservices.HiddenActivity"
246-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1f27b26140701f2c8d05427d7ea619b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
247            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
247-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1f27b26140701f2c8d05427d7ea619b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
248            android:enabled="true"
248-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1f27b26140701f2c8d05427d7ea619b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
249            android:exported="false"
249-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1f27b26140701f2c8d05427d7ea619b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
250            android:fitsSystemWindows="true"
250-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1f27b26140701f2c8d05427d7ea619b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
251            android:theme="@style/Theme.Hidden" >
251-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1f27b26140701f2c8d05427d7ea619b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
252        </activity>
253        <activity
253-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a77d04ca1dbe4ff654eca1293dcf4153\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
254            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
254-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a77d04ca1dbe4ff654eca1293dcf4153\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
255            android:excludeFromRecents="true"
255-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a77d04ca1dbe4ff654eca1293dcf4153\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
256            android:exported="false"
256-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a77d04ca1dbe4ff654eca1293dcf4153\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
257            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
257-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a77d04ca1dbe4ff654eca1293dcf4153\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
258        <!--
259            Service handling Google Sign-In user revocation. For apps that do not integrate with
260            Google Sign-In, this service will never be started.
261        -->
262        <service
262-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a77d04ca1dbe4ff654eca1293dcf4153\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
263            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
263-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a77d04ca1dbe4ff654eca1293dcf4153\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
264            android:exported="true"
264-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a77d04ca1dbe4ff654eca1293dcf4153\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
265            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
265-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a77d04ca1dbe4ff654eca1293dcf4153\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
266            android:visibleToInstantApps="true" />
266-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a77d04ca1dbe4ff654eca1293dcf4153\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
267
268        <receiver
268-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
269            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
269-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
270            android:enabled="true"
270-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
271            android:exported="false" >
271-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
272        </receiver>
273
274        <service
274-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
275            android:name="com.google.android.gms.measurement.AppMeasurementService"
275-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
276            android:enabled="true"
276-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
277            android:exported="false" />
277-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
278        <service
278-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
279            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
279-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
280            android:enabled="true"
280-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
281            android:exported="false"
281-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
282            android:permission="android.permission.BIND_JOB_SERVICE" />
282-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
283
284        <activity
284-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7312d9ce0e64b42e563cee99ce0c7cbc\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
285            android:name="com.google.android.gms.common.api.GoogleApiActivity"
285-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7312d9ce0e64b42e563cee99ce0c7cbc\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
286            android:exported="false"
286-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7312d9ce0e64b42e563cee99ce0c7cbc\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
287            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
287-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7312d9ce0e64b42e563cee99ce0c7cbc\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
288
289        <provider
289-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a338e36f8e4bb82b332b6f077ecfc270\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
290            android:name="androidx.startup.InitializationProvider"
290-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a338e36f8e4bb82b332b6f077ecfc270\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
291            android:authorities="com.hydraiot.app.androidx-startup"
291-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a338e36f8e4bb82b332b6f077ecfc270\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
292            android:exported="false" >
292-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a338e36f8e4bb82b332b6f077ecfc270\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
293            <meta-data
293-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a338e36f8e4bb82b332b6f077ecfc270\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
294                android:name="androidx.emoji2.text.EmojiCompatInitializer"
294-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a338e36f8e4bb82b332b6f077ecfc270\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
295                android:value="androidx.startup" />
295-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a338e36f8e4bb82b332b6f077ecfc270\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
296            <meta-data
296-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\695321ee91075e9a75fbbac8373949a3\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
297                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
297-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\695321ee91075e9a75fbbac8373949a3\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
298                android:value="androidx.startup" />
298-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\695321ee91075e9a75fbbac8373949a3\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
299            <meta-data
299-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
300                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
300-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
301                android:value="androidx.startup" />
301-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
302        </provider>
303
304        <uses-library
304-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8204ee1f1962fe20a95999cc7181b1ae\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
305            android:name="android.ext.adservices"
305-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8204ee1f1962fe20a95999cc7181b1ae\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
306            android:required="false" />
306-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8204ee1f1962fe20a95999cc7181b1ae\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
307
308        <meta-data
308-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a1c3fd62b5fb636be47a17086a4f32c\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
309            android:name="com.google.android.gms.version"
309-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a1c3fd62b5fb636be47a17086a4f32c\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
310            android:value="@integer/google_play_services_version" />
310-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a1c3fd62b5fb636be47a17086a4f32c\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
311
312        <receiver
312-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
313            android:name="androidx.profileinstaller.ProfileInstallReceiver"
313-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
314            android:directBootAware="false"
314-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
315            android:enabled="true"
315-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
316            android:exported="true"
316-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
317            android:permission="android.permission.DUMP" >
317-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
318            <intent-filter>
318-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
319                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
319-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
319-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
320            </intent-filter>
321            <intent-filter>
321-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
322                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
322-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
322-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
323            </intent-filter>
324            <intent-filter>
324-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
325                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
325-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
325-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
326            </intent-filter>
327            <intent-filter>
327-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
328                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
328-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
328-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
329            </intent-filter>
330        </receiver>
331
332        <service
332-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b56c875c2526b9b55356cbfc850a82c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
333            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
333-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b56c875c2526b9b55356cbfc850a82c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
334            android:exported="false" >
334-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b56c875c2526b9b55356cbfc850a82c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
335            <meta-data
335-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b56c875c2526b9b55356cbfc850a82c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
336                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
336-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b56c875c2526b9b55356cbfc850a82c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
337                android:value="cct" />
337-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b56c875c2526b9b55356cbfc850a82c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
338        </service>
339        <service
339-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
340            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
340-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
341            android:exported="false"
341-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
342            android:permission="android.permission.BIND_JOB_SERVICE" >
342-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
343        </service>
344
345        <receiver
345-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
346            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
346-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
347            android:exported="false" />
347-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
348
349        <meta-data
349-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\583dec540f305a60c79ca1a1ad68882f\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
350            android:name="com.facebook.soloader.enabled"
350-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\583dec540f305a60c79ca1a1ad68882f\transformed\soloader-0.12.1\AndroidManifest.xml:13:13-57
351            android:value="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
351-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\583dec540f305a60c79ca1a1ad68882f\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
352        <activity
352-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e689ec2803e3f0638b4bfb915e2ad63\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
353            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
353-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e689ec2803e3f0638b4bfb915e2ad63\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
354            android:exported="false"
354-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e689ec2803e3f0638b4bfb915e2ad63\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
355            android:stateNotNeeded="true"
355-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e689ec2803e3f0638b4bfb915e2ad63\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
356            android:theme="@style/Theme.PlayCore.Transparent" />
356-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e689ec2803e3f0638b4bfb915e2ad63\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
357    </application>
358
359</manifest>

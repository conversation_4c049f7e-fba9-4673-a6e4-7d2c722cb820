# ninja log v5
31921	34742	7701363040287299	E:/HYDRA-IOT/HydraApp/android/app/build/intermediates/cxx/Debug/1n1k36bs/obj/x86/libappmodules.so	812fdd70c357ebd1
15118	24815	7701142765975536	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/ComponentDescriptors.cpp.o	9845394372013a74
1	25	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/x86/CMakeFiles/cmake.verify_globs	c8bb57b29a5afa4e
7723	13330	7701142650379478	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o	8e5760a1d7665224
43095	44208	7701142957563233	E:/HYDRA-IOT/HydraApp/android/app/build/intermediates/cxx/Debug/1n1k36bs/obj/x86/libreact_codegen_safeareacontext.so	3f242eb1c6f01571
8870	15117	7701142668952604	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	add1031fa7f8d290
8894	17111	7701142688578387	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	412102e2810b851b
59076	65229	7701143170029446	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	d19b5875f73f46e1
65051	71424	7701143232241044	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	2ea45e414a8156bb
20	11285	7701362819357827	CMakeFiles/appmodules.dir/OnLoad.cpp.o	fe52ffa1e090245a
7259	17064	7701142687272120	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	52705e8175504e3
39	492	7701465265345345	build.ninja	8192a7b97726a14f
18826	24103	7701142759045689	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/States.cpp.o	fd18df1046b739d1
13331	20502	7701142722918569	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	d00b65a693f860fb
61294	62358	7701143138334855	E:/HYDRA-IOT/HydraApp/android/app/build/intermediates/cxx/Debug/1n1k36bs/obj/x86/libreact_codegen_rnscreens.so	5a0cf23a10600b23
39288	47233	7701142990203096	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/50238a92379787fd4f2f91cf6600c217/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	1b73a97f69f2e3a1
31446	36162	7701142879637664	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/550838433058dc1fd573b81ffaad7ac7/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o	b54accd0c6d4ffdf
8624	17906	7701142696194475	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	6597702266d625a9
11001	18826	7701142706261345	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	884f8771c80d2bd3
63266	69027	7701143208317537	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	63e0718854eeac7b
20502	30152	7701142818138120	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o	fe83c2b1cd067392
23858	30055	7701142818138120	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o	f751c26f6a1b21a0
25534	32459	7701142842411214	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o	1ae37e26c582b688
26900	33087	7701142849043636	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o	a3161b1aac730781
67954	73677	7701143254988877	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a75f7d151350c2ad12e3fcc23620b108/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o	5f7df50bb7e0969d
24104	29368	7701142811590523	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o	9e42da9c3c1aa65a
65550	70605	7701143223789378	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a75f7d151350c2ad12e3fcc23620b108/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o	6c4f4f9905414333
25440	32143	7701142839304373	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o	58280184ee2b8ef6
24815	31445	7701142832382127	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o	f5f617fd03fea1e
37525	44991	7701142967495234	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	efc3217d4ec9b708
30195	37524	7701142893060627	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/847453d9e4667eb7e9460afd25ed1195/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o	b3ce3183c187173d
32459	38748	7701142905368908	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9c69b1f7d8ca70e3a7cc148200b22236/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o	34cd9167e6c83be0
30377	38339	7701142901099639	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/550838433058dc1fd573b81ffaad7ac7/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o	7552d9973dbe2314
30056	37082	7701142888545291	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/550838433058dc1fd573b81ffaad7ac7/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o	dbf8582694722ae5
29368	39287	7701142910395718	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/891b7855b2d125d04ac7afa152cad07e/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	6b82a082d56732b0
17065	23858	7701142755134054	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/EventEmitters.cpp.o	5299c6f867b1a925
64883	71969	7701143237746141	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o	aedf514ec1024a04
51999	63266	7701143150417304	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a94ba4a7694248275b58ea6e1b34e234/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o	bb7b4d143639a30a
33088	39238	7701142910325655	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8311d7994bdb89babd489a4af328892e/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o	c381325eda1d913b
41276	48291	7701143000791304	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/598dc9a4de586092988288a9ed8b2a9c/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	ea0e0cd56de2b497
36162	43095	7701142948596819	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/847453d9e4667eb7e9460afd25ed1195/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o	25f2660a967e626a
75471	75606	7701143274284169	E:/HYDRA-IOT/HydraApp/android/app/build/intermediates/cxx/Debug/1n1k36bs/obj/x86/libreact_codegen_rnsvg.so	e5eb2a5021509b60
60719	67954	7701143197379902	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	39a5aba5b03bfb15
32143	41276	7701142929925525	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b7046d699b6e0fe79e4beb14c8a51d24/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o	7ff92f880b3aa8df
48425	61293	7701143129980730	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ad0053095b3309824e38725beb230ec4/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	9080cb15f396121e
23	31920	7701363022875308	CMakeFiles/appmodules.dir/E_/HYDRA-IOT/HydraApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	7810c60def585a1
39239	48425	7701143001886630	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	54efc79fdd4b2966
44991	53516	7701143052170325	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	bfb46223839fbfb6
44878	51801	7701143035899483	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	fba1af4bd8e865b9
62359	67585	7701143193597602	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	68240d88d29e29c1
47234	55482	7701143072654911	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	546d3f782bd20cf2
55482	65549	7701143173335376	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o	9b90326ba9d07e83
37083	44878	7701142965865896	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	bbcc450b48bcdd7a
65230	71439	7701143232280997	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a75f7d151350c2ad12e3fcc23620b108/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o	9e26dced26c71409
38340	47182	7701142988341478	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	3cad96499d72493
47182	56319	7701143080862901	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	730b39fcbe67ea4f
44208	50575	7701143023457536	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	774210e929aab730
44015	51999	7701143037204851	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	ff91d3d138278132
66998	72347	7701143241537999	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a75f7d151350c2ad12e3fcc23620b108/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o	cb2ab1783aa0395d
67585	75471	7701143272708997	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a75f7d151350c2ad12e3fcc23620b108/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o	4e4311acea38023d
48292	59657	7701143113503935	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/50238a92379787fd4f2f91cf6600c217/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	8fcdaa5186fd3b3c
59658	66997	7701143187595181	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	6ee9f6c68558dcc5
53517	64883	7701143166298145	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o	d4e912b50c1a1a15
51801	59075	7701143108471423	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o	598fd55a99744770
38749	44014	7701142957823223	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	956b11d5e6cb8bcd
56320	65050	7701143168288948	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o	92b756448e84a0be
50576	60719	7701143124264303	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o	59d6ecd013adb68a
17111	25439	7701142772286048	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/ShadowNodes.cpp.o	9082b722c9fc741b
15008	23865	7701142756094637	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/RNImagePickerSpec-generated.cpp.o	d80203089cbfa505
17907	25534	7701142773162176	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/RNImagePickerSpecJSI-generated.cpp.o	3fd66d2c58f8d742
23866	30377	7701142821247031	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/Props.cpp.o	7f530957db31db73
0	19	0	clean	1ffa0f1d946a52ac
59	6334	7701142580477325	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	2f01359deeb1455a
45	7723	7701142595023746	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	4a68fe2601a3479a
50	7258	7701142590485900	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	3cca715d6388bea6
53	8624	7701142604142074	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	8cb7830cd4e63f51
62	8870	7701142606471656	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	ab1f46eae14b36c5
47	8894	7701142606941650	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	346678f2ad60704f
6335	15008	7701142667028292	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	16d3bb65a1983aba
1	28	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/x86/CMakeFiles/cmake.verify_globs	c8bb57b29a5afa4e
1	186	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/x86/CMakeFiles/cmake.verify_globs	c8bb57b29a5afa4e
1	47	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/x86/CMakeFiles/cmake.verify_globs	c8bb57b29a5afa4e
1	42	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/x86/CMakeFiles/cmake.verify_globs	c8bb57b29a5afa4e
1	37	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/x86/CMakeFiles/cmake.verify_globs	c8bb57b29a5afa4e

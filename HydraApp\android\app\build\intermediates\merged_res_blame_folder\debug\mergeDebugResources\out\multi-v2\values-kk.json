{"logs": [{"outputFile": "com.hydraiot.app-mergeDebugResources-51:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa0e231f4d27dc8a031f0119595b6f1\\transformed\\material-1.6.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,307,399,511,593,657,752,822,885,992,1059,1120,1187,1249,1303,1417,1476,1537,1591,1666,1792,1880,1969,2081,2153,2226,2315,2382,2448,2519,2596,2682,2754,2830,2911,2981,3068,3140,3231,3324,3398,3473,3565,3617,3683,3767,3853,3915,3979,4042,4146,4246,4340,4441", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,78,91,111,81,63,94,69,62,106,66,60,66,61,53,113,58,60,53,74,125,87,88,111,71,72,88,66,65,70,76,85,71,75,80,69,86,71,90,92,73,74,91,51,65,83,85,61,63,62,103,99,93,100,83", "endOffsets": "223,302,394,506,588,652,747,817,880,987,1054,1115,1182,1244,1298,1412,1471,1532,1586,1661,1787,1875,1964,2076,2148,2221,2310,2377,2443,2514,2591,2677,2749,2825,2906,2976,3063,3135,3226,3319,3393,3468,3560,3612,3678,3762,3848,3910,3974,4037,4141,4241,4335,4436,4520"}, "to": {"startLines": "2,35,43,44,45,66,67,71,74,76,77,78,79,80,81,82,83,84,85,86,87,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3199,3992,4084,4196,6681,6745,7157,7380,7513,7620,7687,7748,7815,7877,7931,8045,8104,8165,8219,8294,8488,8576,8665,8777,8849,8922,9011,9078,9144,9215,9292,9378,9450,9526,9607,9677,9764,9836,9927,10020,10094,10169,10261,10313,10379,10463,10549,10611,10675,10738,10842,10942,11036,11137", "endLines": "5,35,43,44,45,66,67,71,74,76,77,78,79,80,81,82,83,84,85,86,87,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "12,78,91,111,81,63,94,69,62,106,66,60,66,61,53,113,58,60,53,74,125,87,88,111,71,72,88,66,65,70,76,85,71,75,80,69,86,71,90,92,73,74,91,51,65,83,85,61,63,62,103,99,93,100,83", "endOffsets": "273,3273,4079,4191,4273,6740,6835,7222,7438,7615,7682,7743,7810,7872,7926,8040,8099,8160,8214,8289,8415,8571,8660,8772,8844,8917,9006,9073,9139,9210,9287,9373,9445,9521,9602,9672,9759,9831,9922,10015,10089,10164,10256,10308,10374,10458,10544,10606,10670,10733,10837,10937,11031,11132,11216"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ff5b0ce559ae890269420b3dec0e4060\\transformed\\react-android-0.79.2-debug\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,141,211,294,364,432,507", "endColumns": "85,69,82,69,67,74,72", "endOffsets": "136,206,289,359,427,502,575"}, "to": {"startLines": "46,72,73,75,88,124,125", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4278,7227,7297,7443,8420,11303,11378", "endColumns": "85,69,82,69,67,74,72", "endOffsets": "4359,7292,7375,7508,8483,11373,11446"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd1664c1c7ce01ff711fc15460fc5485\\transformed\\credentials-1.2.0-rc01\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,116", "endOffsets": "163,280"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2969,3082", "endColumns": "112,116", "endOffsets": "3077,3194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7312d9ce0e64b42e563cee99ce0c7cbc\\transformed\\play-services-base-18.5.0\\res\\values-kk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,441,563,665,801,923,1042,1147,1308,1410,1563,1688,1837,1990,2049,2104", "endColumns": "98,148,121,101,135,121,118,104,160,101,152,124,148,152,58,54,73", "endOffsets": "291,440,562,664,800,922,1041,1146,1307,1409,1562,1687,1836,1989,2048,2103,2177"}, "to": {"startLines": "47,48,49,50,51,52,53,54,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4364,4467,4620,4746,4852,4992,5118,5241,5514,5679,5785,5942,6071,6224,6381,6444,6503", "endColumns": "102,152,125,105,139,125,122,108,164,105,156,128,152,156,62,58,77", "endOffsets": "4462,4615,4741,4847,4987,5113,5236,5345,5674,5780,5937,6066,6219,6376,6439,6498,6576"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af63e50a53e992ea9c751fc2deb2fc57\\transformed\\appcompat-1.7.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "278,386,491,601,686,792,911,991,1068,1159,1252,1347,1441,1541,1634,1729,1826,1917,2008,2089,2194,2297,2395,2502,2608,2708,2874,11221", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "381,486,596,681,787,906,986,1063,1154,1247,1342,1436,1536,1629,1724,1821,1912,2003,2084,2189,2292,2390,2497,2603,2703,2869,2964,11298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3135f157d17fbe3966fe84b667792db9\\transformed\\core-1.13.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "36,37,38,39,40,41,42,126", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3278,3373,3475,3577,3680,3784,3881,11451", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "3368,3470,3572,3675,3779,3876,3987,11547"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79f6ab95733be72bb6af37d95e29f537\\transformed\\browser-1.4.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,259,367", "endColumns": "99,103,107,104", "endOffsets": "150,254,362,467"}, "to": {"startLines": "65,68,69,70", "startColumns": "4,4,4,4", "startOffsets": "6581,6840,6944,7052", "endColumns": "99,103,107,104", "endOffsets": "6676,6939,7047,7152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0a1c3fd62b5fb636be47a17086a4f32c\\transformed\\play-services-basement-18.5.0\\res\\values-kk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "55", "startColumns": "4", "startOffsets": "5350", "endColumns": "163", "endOffsets": "5509"}}]}]}
// App theme constants

export const COLORS = {
  // Primary colors - Modern green palette
  primary: '#2E7D32',
  primaryDark: '#1B5E20',
  primaryLight: '#4CAF50',
  primaryLighter: '#81C784',
  primaryLightest: '#C8E6C9',

  // Secondary colors - Complementary blue
  secondary: '#1976D2',
  secondaryDark: '#0D47A1',
  secondaryLight: '#42A5F5',
  secondaryLighter: '#90CAF9',
  secondaryLightest: '#E3F2FD',

  // Accent colors
  accent: '#00ACC1',
  accentDark: '#00838F',
  accentLight: '#26C6DA',

  // Status colors with better contrast
  success: '#2E7D32',
  successLight: '#4CAF50',
  warning: '#F57C00',
  warningLight: '#FF9800',
  danger: '#D32F2F',
  dangerLight: '#F44336',
  info: '#1976D2',
  infoLight: '#2196F3',

  // Neutral colors - Enhanced palette
  white: '#FFFFFF',
  black: '#000000',
  gray: '#757575',
  gray100: '#F5F5F5',
  gray200: '#EEEEEE',
  gray300: '#E0E0E0',
  gray400: '#BDBDBD',
  gray500: '#9E9E9E',
  gray600: '#757575',
  gray700: '#616161',
  gray800: '#424242',
  gray900: '#212121',

  // Background colors
  background: '#FAFAFA',
  backgroundDark: '#F5F5F5',
  card: '#FFFFFF',
  cardElevated: '#FFFFFF',
  surface: '#FFFFFF',

  // Text colors
  textPrimary: '#212121',
  textSecondary: '#757575',
  textTertiary: '#9E9E9E',
  textLight: '#FFFFFF',
  textOnPrimary: '#FFFFFF',
  textOnSecondary: '#FFFFFF',

  // Transparent colors
  transparent: 'transparent',
  semiTransparent: 'rgba(0, 0, 0, 0.5)',
  overlay: 'rgba(0, 0, 0, 0.3)',

  // Gradient colors
  gradientPrimary: ['#2E7D32', '#4CAF50'],
  gradientSecondary: ['#1976D2', '#42A5F5'],
  gradientSuccess: ['#2E7D32', '#66BB6A'],
  gradientWarning: ['#F57C00', '#FFB74D'],
  gradientDanger: ['#D32F2F', '#EF5350'],
  gradientBackground: ['#FAFAFA', '#F5F5F5'],
};

export const FONTS = {
  // Font families
  regular: 'Roboto-Regular',
  medium: 'Roboto-Medium',
  bold: 'Roboto-Bold',
  light: 'Roboto-Light',

  // Font sizes
  h1: 30,
  h2: 24,
  h3: 20,
  h4: 18,
  h5: 16,
  h6: 14,
  body1: 16,
  body2: 14,
  body3: 12,
  small: 10,
};

export const SIZES = {
  // Dimensions
  base: 8,
  xs: 4,
  small: 12,
  medium: 16,
  large: 24,
  xlarge: 32,
  xxlarge: 40,
  xxxlarge: 48,

  // Radius - Enhanced with more options
  radiusXS: 2,
  radiusSmall: 4,
  radiusMedium: 8,
  radiusLarge: 12,
  radiusXLarge: 16,
  radiusXXLarge: 24,
  radiusRound: 50,

  // Spacing - More granular control
  paddingXS: 4,
  paddingSmall: 8,
  paddingMedium: 16,
  paddingLarge: 24,
  paddingXL: 32,
  marginXS: 4,
  marginSmall: 8,
  marginMedium: 16,
  marginLarge: 24,
  marginXL: 32,

  // Icon sizes
  iconXS: 12,
  iconSmall: 16,
  iconMedium: 20,
  iconLarge: 24,
  iconXL: 32,
  iconXXL: 48,

  // Width percentages
  width25: '25%',
  width50: '50%',
  width75: '75%',
  width100: '100%',

  // Height percentages
  height25: '25%',
  height50: '50%',
  height75: '75%',
  height100: '100%',
};

export const SHADOWS = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  small: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 1,
  },
  medium: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 4,
    elevation: 3,
  },
  large: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  xlarge: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.18,
    shadowRadius: 12,
    elevation: 10,
  },
  // Colored shadows for special elements
  primary: {
    shadowColor: '#2E7D32',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  success: {
    shadowColor: '#4CAF50',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  warning: {
    shadowColor: '#FF9800',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  danger: {
    shadowColor: '#F44336',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
};

// Animation constants
export const ANIMATIONS = {
  // Duration
  fast: 150,
  normal: 250,
  slow: 350,

  // Easing
  easeInOut: 'ease-in-out',
  easeIn: 'ease-in',
  easeOut: 'ease-out',

  // Spring configs
  spring: {
    damping: 15,
    stiffness: 150,
  },
  springFast: {
    damping: 20,
    stiffness: 200,
  },
  springSlow: {
    damping: 10,
    stiffness: 100,
  },
};

// Layout constants
export const LAYOUT = {
  // Screen padding
  screenPadding: 16,
  screenPaddingHorizontal: 20,
  screenPaddingVertical: 16,

  // Card dimensions
  cardMinHeight: 120,
  cardMaxWidth: 400,

  // Header heights
  headerHeight: 56,
  tabBarHeight: 60,

  // Button heights
  buttonHeight: 48,
  buttonHeightSmall: 36,
  buttonHeightLarge: 56,
};

export default {
  COLORS,
  FONTS,
  SIZES,
  SHADOWS,
  ANIMATIONS,
  LAYOUT,
};

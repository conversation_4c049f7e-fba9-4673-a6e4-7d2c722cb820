# C/C++ build system timings
generate_cxx_metadata
  [gap of 37ms]
  create-invalidation-state 61ms
  generate-prefab-packages
    [gap of 57ms]
    exec-prefab 12223ms
    [gap of 102ms]
  generate-prefab-packages completed in 12382ms
  execute-generate-process
    exec-configure 4330ms
    [gap of 599ms]
  execute-generate-process completed in 4930ms
  [gap of 886ms]
  write-metadata-json-to-file 380ms
generate_cxx_metadata completed in 18683ms


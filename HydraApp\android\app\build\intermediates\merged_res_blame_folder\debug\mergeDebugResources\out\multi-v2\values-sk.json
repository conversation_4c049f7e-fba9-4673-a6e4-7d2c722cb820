{"logs": [{"outputFile": "com.hydraiot.app-mergeDebugResources-51:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ff5b0ce559ae890269420b3dec0e4060\\transformed\\react-android-0.79.2-debug\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,213,284,355,442,510,579,660,741,828,923,997,1083,1167,1244,1325,1407,1485,1560,1634,1718,1789,1868,1939", "endColumns": "74,82,70,70,86,67,68,80,80,86,94,73,85,83,76,80,81,77,74,73,83,70,78,70,82", "endOffsets": "125,208,279,350,437,505,574,655,736,823,918,992,1078,1162,1239,1320,1402,1480,1555,1629,1713,1784,1863,1934,2017"}, "to": {"startLines": "35,49,74,76,77,79,92,93,94,129,130,131,132,134,135,136,137,138,139,140,141,143,144,145,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3095,4517,7458,7605,7676,7828,8784,8853,8934,11747,11834,11929,12003,12172,12256,12333,12414,12496,12574,12649,12723,12908,12979,13058,13129", "endColumns": "74,82,70,70,86,67,68,80,80,86,94,73,85,83,76,80,81,77,74,73,83,70,78,70,82", "endOffsets": "3165,4595,7524,7671,7758,7891,8848,8929,9010,11829,11924,11998,12084,12251,12328,12409,12491,12569,12644,12718,12802,12974,13053,13124,13207"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7312d9ce0e64b42e563cee99ce0c7cbc\\transformed\\play-services-base-18.5.0\\res\\values-sk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,571,677,829,953,1062,1160,1325,1432,1598,1724,1883,2043,2107,2170", "endColumns": "101,155,119,105,151,123,108,97,164,106,165,125,158,159,63,62,82", "endOffsets": "294,450,570,676,828,952,1061,1159,1324,1431,1597,1723,1882,2042,2106,2169,2252"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4600,4706,4866,4990,5100,5256,5384,5497,5738,5907,6018,6188,6318,6481,6645,6713,6780", "endColumns": "105,159,123,109,155,127,112,101,168,110,169,129,162,163,67,66,86", "endOffsets": "4701,4861,4985,5095,5251,5379,5492,5594,5902,6013,6183,6313,6476,6640,6708,6775,6862"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa0e231f4d27dc8a031f0119595b6f1\\transformed\\material-1.6.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,333,411,503,631,712,777,876,952,1017,1107,1173,1227,1296,1356,1410,1527,1587,1649,1703,1775,1905,1992,2084,2193,2262,2340,2428,2495,2561,2633,2710,2793,2865,2942,3015,3086,3174,3246,3338,3434,3508,3582,3678,3730,3797,3884,3971,4033,4097,4160,4266,4362,4460,4558", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,77,91,127,80,64,98,75,64,89,65,53,68,59,53,116,59,61,53,71,129,86,91,108,68,77,87,66,65,71,76,82,71,76,72,70,87,71,91,95,73,73,95,51,66,86,86,61,63,62,105,95,97,97,78", "endOffsets": "328,406,498,626,707,772,871,947,1012,1102,1168,1222,1291,1351,1405,1522,1582,1644,1698,1770,1900,1987,2079,2188,2257,2335,2423,2490,2556,2628,2705,2788,2860,2937,3010,3081,3169,3241,3333,3429,3503,3577,3673,3725,3792,3879,3966,4028,4092,4155,4261,4357,4455,4553,4632"}, "to": {"startLines": "2,38,46,47,48,69,70,75,78,80,81,82,83,84,85,86,87,88,89,90,91,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3401,4216,4308,4436,6974,7039,7529,7763,7896,7986,8052,8106,8175,8235,8289,8406,8466,8528,8582,8654,9015,9102,9194,9303,9372,9450,9538,9605,9671,9743,9820,9903,9975,10052,10125,10196,10284,10356,10448,10544,10618,10692,10788,10840,10907,10994,11081,11143,11207,11270,11376,11472,11570,11668", "endLines": "7,38,46,47,48,69,70,75,78,80,81,82,83,84,85,86,87,88,89,90,91,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128", "endColumns": "12,77,91,127,80,64,98,75,64,89,65,53,68,59,53,116,59,61,53,71,129,86,91,108,68,77,87,66,65,71,76,82,71,76,72,70,87,71,91,95,73,73,95,51,66,86,86,61,63,62,105,95,97,97,78", "endOffsets": "378,3474,4303,4431,4512,7034,7133,7600,7823,7981,8047,8101,8170,8230,8284,8401,8461,8523,8577,8649,8779,9097,9189,9298,9367,9445,9533,9600,9666,9738,9815,9898,9970,10047,10120,10191,10279,10351,10443,10539,10613,10687,10783,10835,10902,10989,11076,11138,11202,11265,11371,11467,11565,11663,11742"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0a1c3fd62b5fb636be47a17086a4f32c\\transformed\\play-services-basement-18.5.0\\res\\values-sk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5599", "endColumns": "138", "endOffsets": "5733"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd1664c1c7ce01ff711fc15460fc5485\\transformed\\credentials-1.2.0-rc01\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,121", "endOffsets": "159,281"}, "to": {"startLines": "36,37", "startColumns": "4,4", "startOffsets": "3170,3279", "endColumns": "108,121", "endOffsets": "3274,3396"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af63e50a53e992ea9c751fc2deb2fc57\\transformed\\appcompat-1.7.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "383,490,591,702,788,896,1014,1093,1170,1261,1354,1452,1546,1646,1739,1834,1932,2023,2114,2198,2303,2411,2510,2616,2728,2831,2997,12089", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "485,586,697,783,891,1009,1088,1165,1256,1349,1447,1541,1641,1734,1829,1927,2018,2109,2193,2298,2406,2505,2611,2723,2826,2992,3090,12167"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3135f157d17fbe3966fe84b667792db9\\transformed\\core-1.13.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "39,40,41,42,43,44,45,142", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3479,3575,3677,3778,3876,3986,4094,12807", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "3570,3672,3773,3871,3981,4089,4211,12903"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79f6ab95733be72bb6af37d95e29f537\\transformed\\browser-1.4.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,265,380", "endColumns": "106,102,114,101", "endOffsets": "157,260,375,477"}, "to": {"startLines": "68,71,72,73", "startColumns": "4,4,4,4", "startOffsets": "6867,7138,7241,7356", "endColumns": "106,102,114,101", "endOffsets": "6969,7236,7351,7453"}}]}]}
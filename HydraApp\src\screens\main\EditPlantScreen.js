import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import { COLORS, SIZES, FONTS, SHADOWS } from '../../constants/theme';
import { useAuth } from '../../context/AuthContext';
import PlantImage from '../../components/PlantImage';
import Button from '../../components/Button';
import { getAvailablePots, updatePlant, deletePlant } from '../../services/databaseService';

const EditPlantScreen = ({ route, navigation }) => {
  const { plant } = route.params;
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [availablePots, setAvailablePots] = useState([]);
  const [showPotDropdown, setShowPotDropdown] = useState(false);

  // Plant details state - initialize with existing plant data
  const [plantData, setPlantData] = useState({
    name: plant.name || '',
    type: plant.type || '',
    description: plant.description || '',
    potId: plant.potId || '',
    imageBase64: plant.imageBase64 || null,
    conditions: plant.conditions || {
      soilMoisture: { min: 30, max: 70 },
      temperature: { min: 18, max: 28 },
      humidity: { min: 40, max: 80 },
    },
  });

  // Load available pots on component mount
  useEffect(() => {
    loadAvailablePots();
  }, []);

  const loadAvailablePots = async () => {
    try {
      // Pass the current plant ID to exclude it from occupancy check
      const pots = await getAvailablePots(plant.id);
      setAvailablePots(pots);
    } catch (error) {
      console.error('Error loading available pots:', error);
      Alert.alert('Error', 'Failed to load available pots');
    }
  };

  const handleInputChange = (field, value) => {
    setPlantData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleImageChange = (imageData) => {
    if (imageData) {
      // Extract base64 from data URL
      const base64 = imageData.split(',')[1];
      setPlantData(prev => ({
        ...prev,
        imageBase64: base64,
      }));
    } else {
      setPlantData(prev => ({
        ...prev,
        imageBase64: null,
      }));
    }
  };

  const validateForm = () => {
    if (!plantData.name.trim()) {
      Alert.alert('Validation Error', 'Please enter a plant name');
      return false;
    }
    if (!plantData.type.trim()) {
      Alert.alert('Validation Error', 'Please enter a plant type');
      return false;
    }
    if (!plantData.potId) {
      Alert.alert('Validation Error', 'Please select a pot for your plant');
      return false;
    }
    return true;
  };

  const handleUpdatePlant = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      await updatePlant(currentUser.uid, plant.id, plantData, plant.potId);
      Alert.alert(
        'Success',
        'Plant updated successfully!',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      console.error('Error updating plant:', error);
      Alert.alert('Error', 'Failed to update plant. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePlant = () => {
    Alert.alert(
      'Delete Plant',
      `Are you sure you want to delete "${plant.name}"? This action cannot be undone.`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: confirmDeletePlant,
        },
      ]
    );
  };

  const confirmDeletePlant = async () => {
    setDeleteLoading(true);
    try {
      await deletePlant(currentUser.uid, plant.id, plant.potId);
      Alert.alert(
        'Success',
        'Plant deleted successfully!',
        [
          {
            text: 'OK',
            onPress: () => navigation.navigate('MainTabs', { screen: 'Home' }),
          },
        ]
      );
    } catch (error) {
      console.error('Error deleting plant:', error);
      Alert.alert('Error', 'Failed to delete plant. Please try again.');
    } finally {
      setDeleteLoading(false);
    }
  };

  const handleEditConditions = () => {
    navigation.navigate('PlantConditions', {
      plantData,
      onSave: (updatedPlantData) => {
        setPlantData(updatedPlantData);
        navigation.goBack();
      },
    });
  };

  const getSelectedPotName = () => {
    const selectedPot = availablePots.find(pot => pot.id === plantData.potId);
    return selectedPot ? selectedPot.name : 'Select a pot';
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />

      <LinearGradient
        colors={COLORS.gradientPrimary}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.headerGradient}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-left" size={SIZES.iconLarge} color={COLORS.white} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Plant</Text>
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={handleDeletePlant}
          >
            <Icon name="delete" size={SIZES.iconLarge} color={COLORS.white} />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Plant Image Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Plant Photo</Text>
          <View style={styles.imageSection}>
            <PlantImage
              plantId={plant.id}
              imageBase64={plantData.imageBase64}
              plantType={plantData.type || 'default'}
              size="large"
              editable={true}
              onImageChange={handleImageChange}
              style={styles.plantImage}
            />
            <Text style={styles.imageHint}>
              Tap to change photo
            </Text>
          </View>
        </View>

        {/* Basic Details Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Plant Details</Text>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Plant Name *</Text>
            <TextInput
              style={styles.textInput}
              value={plantData.name}
              onChangeText={(value) => handleInputChange('name', value)}
              placeholder="e.g., My Tomato Plant"
              placeholderTextColor={COLORS.gray500}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Plant Type *</Text>
            <TextInput
              style={styles.textInput}
              value={plantData.type}
              onChangeText={(value) => handleInputChange('type', value)}
              placeholder="e.g., Tomato, Basil, Succulent"
              placeholderTextColor={COLORS.gray500}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Description (Optional)</Text>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              value={plantData.description}
              onChangeText={(value) => handleInputChange('description', value)}
              placeholder="Add notes about your plant..."
              placeholderTextColor={COLORS.gray500}
              multiline
              numberOfLines={3}
            />
          </View>
        </View>

        {/* Pot Assignment Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Pot Assignment</Text>
          <Text style={styles.sectionDescription}>
            Choose which pot your plant will be placed in
          </Text>

          <TouchableOpacity
            style={styles.dropdown}
            onPress={() => setShowPotDropdown(!showPotDropdown)}
          >
            <Text style={[
              styles.dropdownText,
              !plantData.potId && styles.dropdownPlaceholder
            ]}>
              {getSelectedPotName()}
            </Text>
            <Icon
              name={showPotDropdown ? "chevron-up" : "chevron-down"}
              size={SIZES.iconMedium}
              color={COLORS.gray600}
            />
          </TouchableOpacity>

          {showPotDropdown && (
            <View style={styles.dropdownList}>
              {availablePots.map((pot) => (
                <TouchableOpacity
                  key={pot.id}
                  style={[
                    styles.dropdownItem,
                    plantData.potId === pot.id && styles.dropdownItemSelected,
                    !pot.available && styles.dropdownItemDisabled
                  ]}
                  onPress={() => {
                    if (pot.available) {
                      handleInputChange('potId', pot.id);
                      setShowPotDropdown(false);
                    }
                  }}
                  disabled={!pot.available}
                >
                  <View style={styles.potInfo}>
                    <Text style={[
                      styles.potName,
                      !pot.available && styles.potNameDisabled
                    ]}>
                      {pot.name}
                    </Text>
                    <Text style={[
                      styles.potStatus,
                      !pot.available && styles.potStatusDisabled
                    ]}>
                      Status: {pot.available ? 'Available' : 'Occupied'}
                    </Text>
                  </View>
                  {pot.available && (
                    <Icon name="check-circle" size={SIZES.iconMedium} color={COLORS.success} />
                  )}
                  {!pot.available && (
                    <Icon name="lock" size={SIZES.iconMedium} color={COLORS.gray400} />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>

        {/* Conditions Section */}
        <View style={styles.section}>
          <View style={styles.conditionsHeader}>
            <Text style={styles.sectionTitle}>Plant Conditions</Text>
            <TouchableOpacity
              style={styles.editConditionsButton}
              onPress={handleEditConditions}
            >
              <Icon name="pencil" size={SIZES.iconSmall} color={COLORS.primary} />
              <Text style={styles.editConditionsText}>Edit</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.conditionsSummary}>
            <View style={styles.conditionItem}>
              <Text style={styles.conditionLabel}>Soil Moisture</Text>
              <Text style={styles.conditionValue}>
                {plantData.conditions.soilMoisture.min}% - {plantData.conditions.soilMoisture.max}%
              </Text>
            </View>
            <View style={styles.conditionItem}>
              <Text style={styles.conditionLabel}>Temperature</Text>
              <Text style={styles.conditionValue}>
                {plantData.conditions.temperature.min}°C - {plantData.conditions.temperature.max}°C
              </Text>
            </View>
            <View style={styles.conditionItem}>
              <Text style={styles.conditionLabel}>Humidity</Text>
              <Text style={styles.conditionValue}>
                {plantData.conditions.humidity.min}% - {plantData.conditions.humidity.max}%
              </Text>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Button
            title="Update Plant"
            onPress={handleUpdatePlant}
            loading={loading}
            style={styles.updateButton}
          />

          <Button
            title="Delete Plant"
            onPress={handleDeletePlant}
            loading={deleteLoading}
            type="danger"
            style={styles.deleteButtonFull}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  headerGradient: {
    paddingBottom: SIZES.paddingMedium,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SIZES.paddingLarge,
    paddingTop: SIZES.paddingLarge,
    paddingBottom: SIZES.paddingMedium,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: FONTS.h3,
    fontWeight: '700',
    color: COLORS.white,
    flex: 1,
    textAlign: 'center',
  },
  deleteButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContent: {
    paddingHorizontal: SIZES.paddingLarge,
    paddingBottom: SIZES.paddingLarge,
  },
  section: {
    marginBottom: SIZES.marginLarge,
  },
  sectionTitle: {
    fontSize: FONTS.h4,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SIZES.marginSmall,
  },
  sectionDescription: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    marginBottom: SIZES.marginMedium,
  },
  imageSection: {
    alignItems: 'center',
    paddingVertical: SIZES.paddingLarge,
  },
  plantImage: {
    marginBottom: SIZES.marginMedium,
  },
  imageHint: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: SIZES.marginMedium,
  },
  inputLabel: {
    fontSize: FONTS.body2,
    fontWeight: '500',
    color: COLORS.textPrimary,
    marginBottom: SIZES.marginSmall,
  },
  textInput: {
    borderWidth: 1,
    borderColor: COLORS.gray300,
    borderRadius: SIZES.radiusMedium,
    paddingHorizontal: SIZES.paddingMedium,
    paddingVertical: SIZES.paddingSmall,
    fontSize: FONTS.body2,
    color: COLORS.textPrimary,
    backgroundColor: COLORS.white,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  dropdown: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: COLORS.gray300,
    borderRadius: SIZES.radiusMedium,
    paddingHorizontal: SIZES.paddingMedium,
    paddingVertical: SIZES.paddingSmall,
    backgroundColor: COLORS.white,
  },
  dropdownText: {
    fontSize: FONTS.body2,
    color: COLORS.textPrimary,
    flex: 1,
  },
  dropdownPlaceholder: {
    color: COLORS.gray500,
  },
  dropdownList: {
    borderWidth: 1,
    borderColor: COLORS.gray300,
    borderRadius: SIZES.radiusMedium,
    backgroundColor: COLORS.white,
    marginTop: SIZES.marginSmall,
    ...SHADOWS.medium,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SIZES.paddingMedium,
    paddingVertical: SIZES.paddingSmall,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray200,
  },
  dropdownItemSelected: {
    backgroundColor: COLORS.primaryLightest,
  },
  dropdownItemDisabled: {
    opacity: 0.5,
  },
  potInfo: {
    flex: 1,
  },
  potName: {
    fontSize: FONTS.body2,
    fontWeight: '500',
    color: COLORS.textPrimary,
  },
  potNameDisabled: {
    color: COLORS.gray400,
  },
  potStatus: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    marginTop: 2,
  },
  potStatusDisabled: {
    color: COLORS.gray400,
  },
  conditionsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.marginMedium,
  },
  editConditionsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SIZES.paddingSmall,
    paddingVertical: SIZES.paddingXS,
    borderRadius: SIZES.radiusSmall,
    backgroundColor: COLORS.primaryLightest,
  },
  editConditionsText: {
    fontSize: FONTS.body3,
    color: COLORS.primary,
    marginLeft: SIZES.marginXS,
    fontWeight: '500',
  },
  conditionsSummary: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radiusMedium,
    padding: SIZES.paddingMedium,
    ...SHADOWS.small,
  },
  conditionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SIZES.paddingSmall,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray200,
  },
  conditionLabel: {
    fontSize: FONTS.body2,
    color: COLORS.textSecondary,
  },
  conditionValue: {
    fontSize: FONTS.body2,
    fontWeight: '500',
    color: COLORS.textPrimary,
  },
  actionButtons: {
    marginTop: SIZES.marginLarge,
  },
  updateButton: {
    marginBottom: SIZES.marginMedium,
  },
  deleteButtonFull: {
    // Additional styles for delete button if needed
  },
});

export default EditPlantScreen;

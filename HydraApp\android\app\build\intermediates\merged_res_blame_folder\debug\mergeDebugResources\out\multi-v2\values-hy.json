{"logs": [{"outputFile": "com.hydraiot.app-mergeDebugResources-51:/values-hy/values-hy.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa0e231f4d27dc8a031f0119595b6f1\\transformed\\material-1.6.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,300,395,525,606,670,767,852,914,1001,1065,1126,1193,1254,1308,1430,1487,1547,1601,1682,1817,1901,1986,2092,2167,2242,2337,2404,2470,2544,2624,2710,2781,2857,2933,3010,3098,3178,3274,3370,3444,3522,3622,3673,3742,3829,3920,3982,4046,4109,4214,4315,4415,4520", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,79,94,129,80,63,96,84,61,86,63,60,66,60,53,121,56,59,53,80,134,83,84,105,74,74,94,66,65,73,79,85,70,75,75,76,87,79,95,95,73,77,99,50,68,86,90,61,63,62,104,100,99,104,84", "endOffsets": "215,295,390,520,601,665,762,847,909,996,1060,1121,1188,1249,1303,1425,1482,1542,1596,1677,1812,1896,1981,2087,2162,2237,2332,2399,2465,2539,2619,2705,2776,2852,2928,3005,3093,3173,3269,3365,3439,3517,3617,3668,3737,3824,3915,3977,4041,4104,4209,4310,4410,4515,4600"}, "to": {"startLines": "2,35,43,44,45,66,67,71,74,76,77,78,79,80,81,82,83,84,85,86,87,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3206,4006,4101,4231,6808,6872,7286,7520,7650,7737,7801,7862,7929,7990,8044,8166,8223,8283,8337,8418,8625,8709,8794,8900,8975,9050,9145,9212,9278,9352,9432,9518,9589,9665,9741,9818,9906,9986,10082,10178,10252,10330,10430,10481,10550,10637,10728,10790,10854,10917,11022,11123,11223,11328", "endLines": "5,35,43,44,45,66,67,71,74,76,77,78,79,80,81,82,83,84,85,86,87,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "12,79,94,129,80,63,96,84,61,86,63,60,66,60,53,121,56,59,53,80,134,83,84,105,74,74,94,66,65,73,79,85,70,75,75,76,87,79,95,95,73,77,99,50,68,86,90,61,63,62,104,100,99,104,84", "endOffsets": "265,3281,4096,4226,4307,6867,6964,7366,7577,7732,7796,7857,7924,7985,8039,8161,8218,8278,8332,8413,8548,8704,8789,8895,8970,9045,9140,9207,9273,9347,9427,9513,9584,9660,9736,9813,9901,9981,10077,10173,10247,10325,10425,10476,10545,10632,10723,10785,10849,10912,11017,11118,11218,11323,11408"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ff5b0ce559ae890269420b3dec0e4060\\transformed\\react-android-0.79.2-debug\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,132,200,281,349,421,496", "endColumns": "76,67,80,67,71,74,73", "endOffsets": "127,195,276,344,416,491,565"}, "to": {"startLines": "46,72,73,75,88,124,125", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4312,7371,7439,7582,8553,11496,11571", "endColumns": "76,67,80,67,71,74,73", "endOffsets": "4384,7434,7515,7645,8620,11566,11640"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3135f157d17fbe3966fe84b667792db9\\transformed\\core-1.13.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "36,37,38,39,40,41,42,126", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3286,3386,3491,3589,3688,3793,3895,11645", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "3381,3486,3584,3683,3788,3890,4001,11741"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79f6ab95733be72bb6af37d95e29f537\\transformed\\browser-1.4.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,262,373", "endColumns": "103,102,110,102", "endOffsets": "154,257,368,471"}, "to": {"startLines": "65,68,69,70", "startColumns": "4,4,4,4", "startOffsets": "6704,6969,7072,7183", "endColumns": "103,102,110,102", "endOffsets": "6803,7067,7178,7281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af63e50a53e992ea9c751fc2deb2fc57\\transformed\\appcompat-1.7.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "270,378,478,588,677,783,900,982,1062,1153,1246,1341,1435,1535,1628,1723,1817,1908,1999,2082,2188,2294,2393,2503,2611,2712,2882,11413", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "373,473,583,672,778,895,977,1057,1148,1241,1336,1430,1530,1623,1718,1812,1903,1994,2077,2183,2289,2388,2498,2606,2707,2877,2974,11491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7312d9ce0e64b42e563cee99ce0c7cbc\\transformed\\play-services-base-18.5.0\\res\\values-hy\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,586,690,840,972,1095,1204,1367,1471,1635,1767,1925,2087,2148,2211", "endColumns": "101,160,129,103,149,131,122,108,162,103,163,131,157,161,60,62,77", "endOffsets": "294,455,585,689,839,971,1094,1203,1366,1470,1634,1766,1924,2086,2147,2210,2288"}, "to": {"startLines": "47,48,49,50,51,52,53,54,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4389,4495,4660,4794,4902,5056,5192,5319,5583,5750,5858,6026,6162,6324,6490,6555,6622", "endColumns": "105,164,133,107,153,135,126,112,166,107,167,135,161,165,64,66,81", "endOffsets": "4490,4655,4789,4897,5051,5187,5314,5427,5745,5853,6021,6157,6319,6485,6550,6617,6699"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd1664c1c7ce01ff711fc15460fc5485\\transformed\\credentials-1.2.0-rc01\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,113", "endOffsets": "163,277"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2979,3092", "endColumns": "112,113", "endOffsets": "3087,3201"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0a1c3fd62b5fb636be47a17086a4f32c\\transformed\\play-services-basement-18.5.0\\res\\values-hy\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "55", "startColumns": "4", "startOffsets": "5432", "endColumns": "150", "endOffsets": "5578"}}]}]}
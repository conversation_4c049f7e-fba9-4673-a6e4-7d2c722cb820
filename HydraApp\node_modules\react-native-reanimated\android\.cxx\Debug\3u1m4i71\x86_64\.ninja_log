# ninja log v5
3	157	0	E:/HYDRA-IOT/HydraApp/node_modules/react-native-reanimated/android/.cxx/Debug/3u1m4i71/x86_64/CMakeFiles/cmake.verify_globs	4f27a1799188040
71	3270	7701140382888150	src/main/cpp/worklets/CMakeFiles/worklets.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o	e3f0fff5f7070ebe
189	3591	7701140386094585	src/main/cpp/worklets/CMakeFiles/worklets.dir/1c87c49df263958b8dabdd0dcd75ffae/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp.o	5ffd8604cb3c7b53
84	3814	7701140388480375	src/main/cpp/worklets/CMakeFiles/worklets.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSISerializer.cpp.o	46167394d50c4489
81	3986	7701140390156191	src/main/cpp/worklets/CMakeFiles/worklets.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSLogger.cpp.o	b5d8b254604ed455
78	4098	7701140391554646	src/main/cpp/worklets/CMakeFiles/worklets.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/AsyncQueue.cpp.o	130044d6cab08dea
87	4115	7701140391734595	src/main/cpp/worklets/CMakeFiles/worklets.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSScheduler.cpp.o	7dc25baf19feef4e
75	5021	7701140400479535	src/main/cpp/worklets/CMakeFiles/worklets.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-reanimated/Common/cpp/worklets/SharedItems/Shareables.cpp.o	d75bbfbc9dec2afd
199	6057	7701140410160996	src/main/cpp/worklets/CMakeFiles/worklets.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedVersion.cpp.o	c6e65fdc3fa2035e
3592	7641	7701140427102637	src/main/cpp/worklets/CMakeFiles/worklets.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/UIScheduler.cpp.o	9ca467b553f0794e
4115	7859	7701140429129948	src/main/cpp/worklets/CMakeFiles/worklets.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp.o	d5456bbb71b0ce41
6057	8001	7701140430730300	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/PlatformLogger.cpp.o	7c0895c80e9ff3fa
3275	8083	7701140431495586	src/main/cpp/worklets/CMakeFiles/worklets.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o	96b8e6c1c2dedccf
3815	8206	7701140432720771	src/main/cpp/worklets/CMakeFiles/worklets.dir/1c87c49df263958b8dabdd0dcd75ffae/react-native-reanimated/Common/cpp/worklets/Tools/WorkletEventHandler.cpp.o	227c62f5f703683c
3987	9055	7701140440935228	src/main/cpp/worklets/CMakeFiles/worklets.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp.o	331b837e47d75cf3
4098	9324	7701140443755127	src/main/cpp/worklets/CMakeFiles/worklets.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp.o	9beefb8540f889d1
5021	11928	7701140469281789	src/main/cpp/worklets/CMakeFiles/worklets.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o	6eb80f429c27f036
9056	13217	7701140482802331	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp.o	1116a65a979f63d1
7641	15214	7701140502683826	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/AndroidUIScheduler.cpp.o	3e2a1bcca71735ed
8083	16176	7701140512197573	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/reanimated/Fabric/PropsRegistry.cpp.o	a27fbc23014a31c4
11928	17779	7701140528190585	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp.o	c9a2fe6605a1394f
7860	17868	7701140529361010	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsModule.cpp.o	383d86eb0ad7e206
8001	18901	7701140531501696	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsOnLoad.cpp.o	fd9cfc4aa28217c3
8206	19035	7701140534095756	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp.o	42029aef4fa800e0
9324	19044	7701140535416096	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp.o	8fdb592e6dfbbaef
17869	23225	7701140582431573	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp.o	bb08b9969833822e
18901	23234	7701140582721500	src/main/cpp/worklets/CMakeFiles/worklets.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp.o	46e82b41724c34e0
19035	24165	7701140592152282	src/main/cpp/worklets/CMakeFiles/worklets.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp.o	f4e5d9ca5fe97b30
23226	25658	7701140607197671	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/reanimated/Tools/FeaturesConfig.cpp.o	bbf6d9bf84daf836
15215	26082	7701140609833490	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp.o	c346f6680d89cf6c
17780	27186	7701140622329719	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp.o	fc0dddfc54666335
19045	29901	7701140649315036	src/main/cpp/worklets/CMakeFiles/worklets.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o	ac9acbb229c99012
16177	30667	7701140656674305	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp.o	d7e4ae462282d926
23234	30792	7701140658370455	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o	5591ab3543586c26
29901	31646	7701140663622244	../../../../build/intermediates/cxx/Debug/3u1m4i71/obj/x86_64/libworklets.so	ea8f214256e8abfb
24165	32173	7701140671851246	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o	29ab085faca59bef
13217	32334	7701140673341845	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp.o	19cd6a18fa192d10
26083	32596	7701140676479387	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp.o	ab24550f0c219f8
25659	34759	7701140698192206	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/61c1fd976dec436b87dea674b659ab5f/Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp.o	5daf65f3da48e636
30667	38208	7701140732873269	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o	b983a04631fdb31
27186	40096	7701140751373339	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o	310957b489fbb584
40096	40617	7701140756778618	../../../../build/intermediates/cxx/Debug/3u1m4i71/obj/x86_64/libreanimated.so	681af874806f2f7b
2	64	0	E:/HYDRA-IOT/HydraApp/node_modules/react-native-reanimated/android/.cxx/Debug/3u1m4i71/x86_64/CMakeFiles/cmake.verify_globs	4f27a1799188040
2	65	0	E:/HYDRA-IOT/HydraApp/node_modules/react-native-reanimated/android/.cxx/Debug/3u1m4i71/x86_64/CMakeFiles/cmake.verify_globs	4f27a1799188040
2	34	0	E:/HYDRA-IOT/HydraApp/node_modules/react-native-reanimated/android/.cxx/Debug/3u1m4i71/x86_64/CMakeFiles/cmake.verify_globs	4f27a1799188040
2	52	0	E:/HYDRA-IOT/HydraApp/node_modules/react-native-reanimated/android/.cxx/Debug/3u1m4i71/x86_64/CMakeFiles/cmake.verify_globs	4f27a1799188040

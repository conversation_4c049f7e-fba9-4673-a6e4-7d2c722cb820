{"logs": [{"outputFile": "com.hydraiot.app-mergeDebugResources-51:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0a1c3fd62b5fb636be47a17086a4f32c\\transformed\\play-services-basement-18.5.0\\res\\values-nl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5515", "endColumns": "142", "endOffsets": "5653"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa0e231f4d27dc8a031f0119595b6f1\\transformed\\material-1.6.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,301,398,520,601,665,758,837,900,993,1059,1117,1190,1254,1310,1432,1489,1551,1607,1683,1817,1902,1988,2096,2177,2256,2346,2413,2479,2557,2640,2728,2803,2882,2955,3026,3120,3198,3287,3377,3451,3532,3619,3672,3739,3820,3904,3966,4030,4093,4201,4302,4404,4507", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,96,121,80,63,92,78,62,92,65,57,72,63,55,121,56,61,55,75,133,84,85,107,80,78,89,66,65,77,82,87,74,78,72,70,93,77,88,89,73,80,86,52,66,80,83,61,63,62,107,100,101,102,80", "endOffsets": "219,296,393,515,596,660,753,832,895,988,1054,1112,1185,1249,1305,1427,1484,1546,1602,1678,1812,1897,1983,2091,2172,2251,2341,2408,2474,2552,2635,2723,2798,2877,2950,3021,3115,3193,3282,3372,3446,3527,3614,3667,3734,3815,3899,3961,4025,4088,4196,4297,4399,4502,4583"}, "to": {"startLines": "2,36,44,45,46,67,68,73,76,77,78,79,80,81,82,83,84,85,86,87,88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3308,4119,4216,4338,6895,6959,7431,7670,7733,7826,7892,7950,8023,8087,8143,8265,8322,8384,8440,8516,8800,8885,8971,9079,9160,9239,9329,9396,9462,9540,9623,9711,9786,9865,9938,10009,10103,10181,10270,10360,10434,10515,10602,10655,10722,10803,10887,10949,11013,11076,11184,11285,11387,11490", "endLines": "5,36,44,45,46,67,68,73,76,77,78,79,80,81,82,83,84,85,86,87,88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "endColumns": "12,76,96,121,80,63,92,78,62,92,65,57,72,63,55,121,56,61,55,75,133,84,85,107,80,78,89,66,65,77,82,87,74,78,72,70,93,77,88,89,73,80,86,52,66,80,83,61,63,62,107,100,101,102,80", "endOffsets": "269,3380,4211,4333,4414,6954,7047,7505,7728,7821,7887,7945,8018,8082,8138,8260,8317,8379,8435,8511,8645,8880,8966,9074,9155,9234,9324,9391,9457,9535,9618,9706,9781,9860,9933,10004,10098,10176,10265,10355,10429,10510,10597,10650,10717,10798,10882,10944,11008,11071,11179,11280,11382,11485,11566"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af63e50a53e992ea9c751fc2deb2fc57\\transformed\\appcompat-1.7.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "274,392,497,604,689,793,913,991,1067,1159,1253,1348,1442,1542,1636,1732,1827,1919,2011,2093,2204,2307,2406,2521,2635,2738,2893,11884", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "387,492,599,684,788,908,986,1062,1154,1248,1343,1437,1537,1631,1727,1822,1914,2006,2088,2199,2302,2401,2516,2630,2733,2888,2991,11962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ff5b0ce559ae890269420b3dec0e4060\\transformed\\react-android-0.79.2-debug\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,206,274,348,434,508,584,668,747,819,897,975,1049,1136,1220,1297,1368,1438,1527,1605,1690", "endColumns": "75,74,67,73,85,73,75,83,78,71,77,77,73,86,83,76,70,69,88,77,84,73", "endOffsets": "126,201,269,343,429,503,579,663,742,814,892,970,1044,1131,1215,1292,1363,1433,1522,1600,1685,1759"}, "to": {"startLines": "33,47,72,74,75,89,90,125,126,127,128,130,131,132,133,134,135,136,137,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2996,4419,7363,7510,7584,8650,8724,11571,11655,11734,11806,11967,12045,12119,12206,12290,12367,12438,12508,12698,12776,12861", "endColumns": "75,74,67,73,85,73,75,83,78,71,77,77,73,86,83,76,70,69,88,77,84,73", "endOffsets": "3067,4489,7426,7579,7665,8719,8795,11650,11729,11801,11879,12040,12114,12201,12285,12362,12433,12503,12592,12771,12856,12930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7312d9ce0e64b42e563cee99ce0c7cbc\\transformed\\play-services-base-18.5.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,444,568,675,838,961,1080,1182,1356,1458,1623,1745,1904,2082,2146,2205", "endColumns": "103,146,123,106,162,122,118,101,173,101,164,121,158,177,63,58,74", "endOffsets": "296,443,567,674,837,960,1079,1181,1355,1457,1622,1744,1903,2081,2145,2204,2279"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4494,4602,4753,4881,4992,5159,5286,5409,5658,5836,5942,6111,6237,6400,6582,6650,6713", "endColumns": "107,150,127,110,166,126,122,105,177,105,168,125,162,181,67,62,78", "endOffsets": "4597,4748,4876,4987,5154,5281,5404,5510,5831,5937,6106,6232,6395,6577,6645,6708,6787"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3135f157d17fbe3966fe84b667792db9\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "37,38,39,40,41,42,43,138", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3385,3487,3589,3689,3789,3896,4000,12597", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "3482,3584,3684,3784,3891,3995,4114,12693"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79f6ab95733be72bb6af37d95e29f537\\transformed\\browser-1.4.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,98", "endOffsets": "153,254,365,464"}, "to": {"startLines": "66,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "6792,7052,7153,7264", "endColumns": "102,100,110,98", "endOffsets": "6890,7148,7259,7358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd1664c1c7ce01ff711fc15460fc5485\\transformed\\credentials-1.2.0-rc01\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,121", "endOffsets": "164,286"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3072,3186", "endColumns": "113,121", "endOffsets": "3181,3303"}}]}]}
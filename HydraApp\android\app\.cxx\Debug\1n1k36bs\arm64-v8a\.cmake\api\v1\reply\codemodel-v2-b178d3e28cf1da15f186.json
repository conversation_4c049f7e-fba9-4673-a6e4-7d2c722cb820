{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-1e65e3e056e906de375d.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "E:/HYDRA-IOT/HydraApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-cb7bdded7be171f8f285.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "E:/HYDRA-IOT/HydraApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "RNImagePickerSpec_autolinked_build", "jsonFile": "directory-RNImagePickerSpec_autolinked_build-Debug-545c01ce14ca8b981b3a.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "E:/HYDRA-IOT/HydraApp/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-Debug-919cd52f241c83c130b0.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "E:/HYDRA-IOT/HydraApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [5]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-8638cc4dfdc3b865e12b.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "E:/HYDRA-IOT/HydraApp/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [8]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-8f0e70b8fe7ac2e119a4.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "E:/HYDRA-IOT/HydraApp/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [6]}, {"build": "rnsvg_autolinked_build", "jsonFile": "directory-rnsvg_autolinked_build-Debug-e6785a76f11df55a9071.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "E:/HYDRA-IOT/HydraApp/node_modules/react-native-svg/android/src/main/jni", "targetIndexes": [7]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-Debug-d9b40d1c0fd92c726e8f.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "E:/HYDRA-IOT/HydraApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [2]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-6f9fbab8c008c04aecc0.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_RNImagePickerSpec::@f66ee9a2efecfb28bee4", "jsonFile": "target-react_codegen_RNImagePickerSpec-Debug-5722f465f219a742af03.json", "name": "react_codegen_RNImagePickerSpec", "projectIndex": 0}, {"directoryIndex": 8, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-Debug-e0758eb4817be9e40994.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-2a29e76d518d20d3344b.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-d96b03fa7c0217532d34.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-Debug-139cbfaf12cdaf74e254.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-6532529a79399b6a8351.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65", "jsonFile": "target-react_codegen_rnsvg-Debug-18030d661c9229e74a86.json", "name": "react_codegen_rnsvg", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-e232319b726ef01a79f3.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/arm64-v8a", "source": "E:/HYDRA-IOT/HydraApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}
import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, SIZES, FONTS, SHADOWS } from '../../constants/theme';
import Input from '../../components/Input';
import Button from '../../components/Button';
import { useAuth } from '../../context/AuthContext';
import { updateUserProfile } from '../../services/databaseService';
import { changePassword } from '../../services/authService';

const SettingsScreen = ({ navigation }) => {
  const { currentUser, userProfile, updateProfile } = useAuth();

  const [name, setName] = useState(userProfile?.displayName || '');
  const [notificationsEnabled, setNotificationsEnabled] = useState(
    userProfile?.settings?.notificationsEnabled || true
  );
  const [alertNotificationsEnabled, setAlertNotificationsEnabled] = useState(
    userProfile?.settings?.alertNotificationsEnabled || true
  );
  const [darkModeEnabled, setDarkModeEnabled] = useState(
    userProfile?.settings?.darkModeEnabled || false
  );

  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const [loading, setLoading] = useState(false);
  const [passwordLoading, setPasswordLoading] = useState(false);

  // Save profile changes
  const handleSaveProfile = async () => {
    try {
      setLoading(true);

      const updatedProfile = {
        displayName: name,
        settings: {
          notificationsEnabled,
          alertNotificationsEnabled,
          darkModeEnabled,
        },
      };

      await updateUserProfile(currentUser.uid, updatedProfile);
      updateProfile(updatedProfile);

      Alert.alert('Success', 'Profile updated successfully');
    } catch (error) {
      console.error('Update profile error:', error);
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Change password
  const handleChangePassword = async () => {
    // Validate passwords
    if (newPassword !== confirmPassword) {
      Alert.alert('Error', 'New passwords do not match');
      return;
    }

    if (newPassword.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters');
      return;
    }

    try {
      setPasswordLoading(true);

      await changePassword(currentPassword, newPassword);

      // Clear password fields
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');

      Alert.alert('Success', 'Password changed successfully');
    } catch (error) {
      console.error('Change password error:', error);
      Alert.alert(
        'Error',
        error.message || 'Failed to change password. Please try again.'
      );
    } finally {
      setPasswordLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />

      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Settings</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Profile Settings</Text>

          <Input
            label="Display Name"
            placeholder="Enter your name"
            value={name}
            onChangeText={setName}
            icon="account"
          />

          <Button
            title="Save Profile"
            onPress={handleSaveProfile}
            loading={loading}
            style={styles.saveButton}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>App Settings</Text>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Icon name="bell" size={24} color={COLORS.textPrimary} />
              <View style={styles.settingTextContainer}>
                <Text style={styles.settingText}>General Notifications</Text>
                <Text style={styles.settingDescription}>
                  Enable app notifications and updates
                </Text>
              </View>
            </View>
            <Switch
              value={notificationsEnabled}
              onValueChange={setNotificationsEnabled}
              trackColor={{ false: COLORS.lightGray, true: COLORS.primaryLight }}
              thumbColor={notificationsEnabled ? COLORS.primary : COLORS.gray}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Icon name="alert-circle" size={24} color={COLORS.warning} />
              <View style={styles.settingTextContainer}>
                <Text style={styles.settingText}>Plant Alert Notifications</Text>
                <Text style={styles.settingDescription}>
                  Get notified when plants need attention
                </Text>
              </View>
            </View>
            <Switch
              value={alertNotificationsEnabled}
              onValueChange={setAlertNotificationsEnabled}
              trackColor={{ false: COLORS.lightGray, true: COLORS.warningLight }}
              thumbColor={alertNotificationsEnabled ? COLORS.warning : COLORS.gray}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Icon name="theme-light-dark" size={24} color={COLORS.textPrimary} />
              <View style={styles.settingTextContainer}>
                <Text style={styles.settingText}>Dark Mode</Text>
                <Text style={styles.settingDescription}>
                  Switch between light and dark themes
                </Text>
              </View>
            </View>
            <Switch
              value={darkModeEnabled}
              onValueChange={setDarkModeEnabled}
              trackColor={{ false: COLORS.lightGray, true: COLORS.primaryLight }}
              thumbColor={darkModeEnabled ? COLORS.primary : COLORS.gray}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Security</Text>

          <Input
            label="Current Password"
            placeholder="Enter current password"
            value={currentPassword}
            onChangeText={setCurrentPassword}
            secureTextEntry
            icon="lock"
          />

          <Input
            label="New Password"
            placeholder="Enter new password"
            value={newPassword}
            onChangeText={setNewPassword}
            secureTextEntry
            icon="lock-reset"
          />

          <Input
            label="Confirm New Password"
            placeholder="Confirm new password"
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            secureTextEntry
            icon="lock-check"
          />

          <Button
            title="Change Password"
            onPress={handleChangePassword}
            loading={passwordLoading}
            style={styles.saveButton}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>About</Text>

          <View style={styles.aboutItem}>
            <Text style={styles.aboutLabel}>App Version</Text>
            <Text style={styles.aboutValue}>1.0.0</Text>
          </View>

          <View style={styles.aboutItem}>
            <Text style={styles.aboutLabel}>Developer</Text>
            <Text style={styles.aboutValue}>Hydra Team</Text>
          </View>

          <TouchableOpacity
            style={styles.linkItem}
            onPress={() => {
              // Open privacy policy
              Alert.alert('Privacy Policy', 'This feature is coming soon!');
            }}
          >
            <Text style={styles.linkText}>Privacy Policy</Text>
            <Icon name="chevron-right" size={20} color={COLORS.primary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.linkItem}
            onPress={() => {
              // Open terms of service
              Alert.alert('Terms of Service', 'This feature is coming soon!');
            }}
          >
            <Text style={styles.linkText}>Terms of Service</Text>
            <Icon name="chevron-right" size={20} color={COLORS.primary} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    paddingHorizontal: SIZES.paddingMedium,
    paddingVertical: SIZES.paddingMedium,
  },
  backButton: {
    padding: SIZES.paddingSmall,
  },
  headerTitle: {
    fontSize: FONTS.h4,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  headerRight: {
    width: 40,
  },
  scrollContent: {
    paddingBottom: SIZES.paddingLarge,
  },
  section: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radiusMedium,
    padding: SIZES.paddingLarge,
    margin: SIZES.marginLarge,
    ...SHADOWS.small,
  },
  sectionTitle: {
    fontSize: FONTS.h4,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: SIZES.marginLarge,
  },
  saveButton: {
    marginTop: SIZES.marginMedium,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: SIZES.paddingLarge,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
    minHeight: 60,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
    marginRight: SIZES.marginMedium,
  },
  settingTextContainer: {
    flex: 1,
    marginLeft: SIZES.marginMedium,
    paddingRight: SIZES.paddingSmall,
  },
  settingText: {
    fontSize: FONTS.body2,
    color: COLORS.textPrimary,
    fontWeight: '600',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    lineHeight: 16,
    paddingRight: SIZES.paddingSmall,
  },
  aboutItem: {
    paddingVertical: SIZES.paddingSmall,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  aboutLabel: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    marginBottom: 4,
  },
  aboutValue: {
    fontSize: FONTS.body2,
    color: COLORS.textPrimary,
  },
  linkItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SIZES.paddingMedium,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  linkText: {
    fontSize: FONTS.body2,
    color: COLORS.primary,
  },
});

export default SettingsScreen;

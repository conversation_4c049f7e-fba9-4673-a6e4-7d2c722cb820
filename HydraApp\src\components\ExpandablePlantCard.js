import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, SIZES, FONTS, SHADOWS } from '../constants/theme';
import PlantImage from './PlantImage';
import WateringControls from './WateringControls';

const ExpandablePlantCard = ({
  plant,
  sensorData,
  isExpanded,
  onExpand,
  onEdit,
  onWateringChange,
}) => {
  const [animation] = useState(new Animated.Value(isExpanded ? 1 : 0));

  React.useEffect(() => {
    Animated.timing(animation, {
      toValue: isExpanded ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [isExpanded, animation]);

  const { name, type } = plant;
  const { soilMoisture, temperature, humidity } = sensorData;

  // Get plant status based on conditions
  const getPlantAnalysis = () => {
    if (!plant.conditions) {
      return {
        overallStatus: 'UNKNOWN',
        overallColor: COLORS.gray500,
        alerts: [],
        hasAlerts: false,
        primaryAlert: null
      };
    }

    const { conditions } = plant;
    const alerts = [];
    
    // Check soil moisture
    const soilTooLow = soilMoisture < conditions.soilMoisture.min;
    const soilTooHigh = soilMoisture > conditions.soilMoisture.max;
    
    // Check temperature
    const tempTooLow = temperature < conditions.temperature.min;
    const tempTooHigh = temperature > conditions.temperature.max;
    
    // Check humidity
    const humidityTooLow = humidity < conditions.humidity.min;
    const humidityTooHigh = humidity > conditions.humidity.max;

    // Generate alerts for each sensor
    if (soilTooLow) {
      alerts.push({
        type: 'soil',
        severity: 'critical',
        icon: 'water-alert',
        message: `Needs watering! (${soilMoisture}% < ${conditions.soilMoisture.min}%)`,
        color: COLORS.danger
      });
    } else if (soilTooHigh) {
      alerts.push({
        type: 'soil',
        severity: 'warning',
        icon: 'water-off',
        message: `Too much water! (${soilMoisture}% > ${conditions.soilMoisture.max}%)`,
        color: COLORS.warning
      });
    }

    if (tempTooLow) {
      alerts.push({
        type: 'temperature',
        severity: 'warning',
        icon: 'thermometer-low',
        message: `Too cold! (${temperature}°C < ${conditions.temperature.min}°C)`,
        color: COLORS.info
      });
    } else if (tempTooHigh) {
      alerts.push({
        type: 'temperature',
        severity: 'warning',
        icon: 'thermometer-high',
        message: `Too hot! (${temperature}°C > ${conditions.temperature.max}°C)`,
        color: COLORS.warning
      });
    }

    if (humidityTooLow) {
      alerts.push({
        type: 'humidity',
        severity: 'warning',
        icon: 'air-humidifier-off',
        message: `Too dry! (${humidity}% < ${conditions.humidity.min}%)`,
        color: COLORS.secondary
      });
    } else if (humidityTooHigh) {
      alerts.push({
        type: 'humidity',
        severity: 'warning',
        icon: 'water-percent',
        message: `Too humid! (${humidity}% > ${conditions.humidity.max}%)`,
        color: COLORS.accent
      });
    }

    // Determine overall status
    let overallStatus = 'GOOD';
    let overallColor = COLORS.success;

    if (alerts.some(alert => alert.severity === 'critical')) {
      overallStatus = 'CRITICAL';
      overallColor = COLORS.danger;
    } else if (alerts.length > 0) {
      overallStatus = 'WARNING';
      overallColor = COLORS.warning;
    }

    const primaryAlert = alerts.find(alert => alert.type === 'soil') || alerts[0] || null;

    return {
      overallStatus,
      overallColor,
      alerts,
      hasAlerts: alerts.length > 0,
      primaryAlert
    };
  };

  const plantAnalysis = getPlantAnalysis();

  const expandedHeight = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 400], // Adjust based on content height
  });

  return (
    <View style={styles.container}>
      {/* Main Card */}
      <TouchableOpacity
        style={styles.card}
        onPress={onExpand}
        activeOpacity={0.7}
      >
        <View style={styles.cardContent}>
          {/* Plant Image */}
          <View style={styles.imageContainer}>
            <PlantImage
              plantId={plant.id}
              imageBase64={plant.imageBase64}
              plantType={type}
              size="medium"
              style={styles.plantImage}
            />
            <View style={[styles.statusIndicator, { backgroundColor: plantAnalysis.overallColor }]}>
              <View style={styles.statusDot} />
            </View>
          </View>

          {/* Plant Info */}
          <View style={styles.plantInfo}>
            <View style={styles.headerRow}>
              <Text style={styles.plantName}>{name}</Text>
              <View style={[styles.statusBadge, { backgroundColor: plantAnalysis.overallColor }]}>
                <Text style={styles.statusBadgeText}>
                  {plantAnalysis.overallStatus}
                </Text>
              </View>
            </View>
            
            <Text style={styles.plantType}>{type}</Text>
            
            {/* Sensor Values */}
            <View style={styles.sensorRow}>
              <View style={styles.sensorItem}>
                <Icon name="water-percent" size={SIZES.iconSmall} color={COLORS.primary} />
                <Text style={styles.sensorValue}>{soilMoisture}%</Text>
              </View>
              <View style={styles.sensorItem}>
                <Icon name="thermometer" size={SIZES.iconSmall} color={COLORS.secondary} />
                <Text style={styles.sensorValue}>{temperature}°C</Text>
              </View>
              <View style={styles.sensorItem}>
                <Icon name="water" size={SIZES.iconSmall} color={COLORS.accent} />
                <Text style={styles.sensorValue}>{humidity}%</Text>
              </View>
            </View>

            {/* Primary Alert */}
            {plantAnalysis.hasAlerts && plantAnalysis.primaryAlert && (
              <View style={[styles.alertContainer, { backgroundColor: plantAnalysis.primaryAlert.color + '15' }]}>
                <Icon 
                  name={plantAnalysis.primaryAlert.icon} 
                  size={SIZES.iconSmall} 
                  color={plantAnalysis.primaryAlert.color} 
                />
                <Text style={[styles.alertText, { color: plantAnalysis.primaryAlert.color }]}>
                  {plantAnalysis.primaryAlert.message}
                </Text>
              </View>
            )}
          </View>

          {/* Expand/Edit Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.editButton}
              onPress={onEdit}
            >
              <Icon name="pencil" size={SIZES.iconSmall} color={COLORS.primary} />
            </TouchableOpacity>
            <View style={styles.expandButton}>
              <Icon 
                name={isExpanded ? "chevron-up" : "chevron-down"} 
                size={SIZES.iconMedium} 
                color={COLORS.gray600} 
              />
            </View>
          </View>
        </View>
      </TouchableOpacity>

      {/* Expanded Content */}
      <Animated.View style={[styles.expandedContent, { height: expandedHeight }]}>
        <View style={styles.expandedInner}>
          {isExpanded && (
            <WateringControls
              plant={plant}
              sensorData={sensorData}
              onWateringChange={onWateringChange}
            />
          )}
        </View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SIZES.marginMedium,
  },
  card: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radiusLarge,
    ...SHADOWS.medium,
  },
  cardContent: {
    flexDirection: 'row',
    padding: SIZES.paddingLarge,
    alignItems: 'center',
  },
  imageContainer: {
    position: 'relative',
    marginRight: SIZES.marginMedium,
  },
  plantImage: {
    // PlantImage component handles its own styling
  },
  statusIndicator: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: COLORS.white,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: COLORS.white,
  },
  plantInfo: {
    flex: 1,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.marginXS,
  },
  plantName: {
    fontSize: FONTS.h4,
    fontWeight: '600',
    color: COLORS.textPrimary,
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: SIZES.paddingSmall,
    paddingVertical: 2,
    borderRadius: SIZES.radiusSmall,
  },
  statusBadgeText: {
    fontSize: FONTS.caption,
    fontWeight: '600',
    color: COLORS.white,
  },
  plantType: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    marginBottom: SIZES.marginMedium,
  },
  sensorRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SIZES.marginSmall,
  },
  sensorItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  sensorValue: {
    fontSize: FONTS.body3,
    fontWeight: '500',
    color: COLORS.textPrimary,
    marginLeft: SIZES.marginXS,
  },
  alertContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SIZES.paddingSmall,
    paddingVertical: SIZES.paddingXS,
    borderRadius: SIZES.radiusSmall,
    marginTop: SIZES.marginXS,
  },
  alertText: {
    fontSize: FONTS.body3,
    fontWeight: '600',
    marginLeft: SIZES.marginXS,
    flex: 1,
  },
  actionButtons: {
    alignItems: 'center',
    marginLeft: SIZES.marginSmall,
  },
  editButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.primaryLightest,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SIZES.marginSmall,
  },
  expandButton: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  expandedContent: {
    overflow: 'hidden',
  },
  expandedInner: {
    paddingHorizontal: SIZES.paddingLarge,
    paddingBottom: SIZES.paddingLarge,
  },
});

export default ExpandablePlantCard;

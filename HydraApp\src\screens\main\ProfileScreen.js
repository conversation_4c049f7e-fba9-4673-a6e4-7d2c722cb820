import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  StatusBar,
  Alert,
  Image,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import { COLORS, SIZES, FONTS, SHADOWS } from '../../constants/theme';
import Button from '../../components/Button';
import PlantImage from '../../components/PlantImage';
import { useAuth } from '../../context/AuthContext';
import { logoutUser } from '../../services/authService';

const ProfileScreen = ({ navigation }) => {
  const { currentUser, userProfile } = useAuth();
  const [loading, setLoading] = useState(false);

  // Handle logout
  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          onPress: async () => {
            try {
              setLoading(true);
              await logoutUser();
              // Auth context will handle navigation after logout
            } catch (error) {
              console.error('Logout error:', error);
              Alert.alert('Error', 'Failed to logout. Please try again.');
            } finally {
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  // Get user initials for avatar
  const getUserInitials = () => {
    if (!userProfile?.displayName) return '?';

    const names = userProfile.displayName.split(' ');
    if (names.length === 1) return names[0].charAt(0).toUpperCase();

    return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />

      <LinearGradient
        colors={COLORS.gradientPrimary}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.headerGradient}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-left" size={SIZES.iconLarge} color={COLORS.white} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Profile</Text>
          <View style={styles.headerSpacer} />
        </View>

        <View style={styles.profileHeader}>
          <View style={styles.avatarContainer}>
            <PlantImage
              plantId="user-profile"
              imageUrl={userProfile?.photoURL}
              plantType="user"
              size="large"
              editable={true}
              style={styles.avatar}
              onImageChange={(newImageUrl) => {
                // Handle profile image change
                console.log('Profile image changed:', newImageUrl);
              }}
            />
          </View>

          <Text style={styles.userName}>{userProfile?.displayName || 'User'}</Text>
          <Text style={styles.userEmail}>{currentUser?.email || 'No email'}</Text>
        </View>
      </LinearGradient>

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >

        <View style={styles.infoSection}>
          <Text style={styles.sectionTitle}>Account Information</Text>

          <View style={styles.infoItem}>
            <View style={styles.infoIconContainer}>
              <Icon name="account" size={20} color={COLORS.primary} />
            </View>
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Name</Text>
              <Text style={styles.infoValue}>{userProfile?.displayName || 'Not set'}</Text>
            </View>
          </View>

          <View style={styles.infoItem}>
            <View style={styles.infoIconContainer}>
              <Icon name="email" size={20} color={COLORS.primary} />
            </View>
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Email</Text>
              <Text style={styles.infoValue}>{currentUser?.email || 'Not set'}</Text>
            </View>
          </View>

          <View style={styles.infoItem}>
            <View style={styles.infoIconContainer}>
              <Icon name="calendar" size={20} color={COLORS.primary} />
            </View>
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Member Since</Text>
              <Text style={styles.infoValue}>{formatDate(userProfile?.createdAt)}</Text>
            </View>
          </View>

          <View style={styles.infoItem}>
            <View style={styles.infoIconContainer}>
              <Icon name="login" size={20} color={COLORS.primary} />
            </View>
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Last Login</Text>
              <Text style={styles.infoValue}>{formatDate(userProfile?.lastLogin)}</Text>
            </View>
          </View>
        </View>

        <View style={styles.actionsSection}>
          <TouchableOpacity
            style={styles.actionItem}
            onPress={() => navigation.navigate('Settings')}
          >
            <Icon name="cog" size={24} color={COLORS.textPrimary} />
            <Text style={styles.actionText}>Settings</Text>
            <Icon name="chevron-right" size={24} color={COLORS.gray} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionItem}
            onPress={() => {
              // Navigate to help/support screen
              Alert.alert('Help & Support', 'This feature is coming soon!');
            }}
          >
            <Icon name="help-circle" size={24} color={COLORS.textPrimary} />
            <Text style={styles.actionText}>Help & Support</Text>
            <Icon name="chevron-right" size={24} color={COLORS.gray} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionItem}
            onPress={() => {
              // Navigate to about screen
              Alert.alert('About', 'Hydra v1.0.0\nSmart Plant Watering System');
            }}
          >
            <Icon name="information" size={24} color={COLORS.textPrimary} />
            <Text style={styles.actionText}>About</Text>
            <Icon name="chevron-right" size={24} color={COLORS.gray} />
          </TouchableOpacity>
        </View>

        <Button
          title="Logout"
          onPress={handleLogout}
          loading={loading}
          type="danger"
          style={styles.logoutButton}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  headerGradient: {
    paddingBottom: SIZES.paddingLarge,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SIZES.paddingLarge,
    paddingTop: SIZES.paddingLarge,
    paddingBottom: SIZES.paddingMedium,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: FONTS.h3,
    fontWeight: '700',
    color: COLORS.white,
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40, // Same width as back button for centering
  },
  scrollContent: {
    paddingBottom: SIZES.paddingLarge,
  },
  profileHeader: {
    alignItems: 'center',
    paddingHorizontal: SIZES.paddingLarge,
    paddingBottom: SIZES.paddingLarge,
  },
  avatarContainer: {
    marginBottom: SIZES.marginMedium,
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 100,
    padding: 4,
  },
  avatar: {
    // PlantImage will handle sizing
  },
  userName: {
    fontSize: FONTS.h2,
    fontWeight: '700',
    color: COLORS.white,
    marginBottom: SIZES.marginXS,
    textAlign: 'center',
  },
  userEmail: {
    fontSize: FONTS.body2,
    color: COLORS.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  infoSection: {
    padding: SIZES.paddingLarge,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  sectionTitle: {
    fontSize: FONTS.h4,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SIZES.marginMedium,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SIZES.marginMedium,
  },
  infoIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SIZES.marginMedium,
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    marginBottom: 2,
  },
  infoValue: {
    fontSize: FONTS.body2,
    color: COLORS.textPrimary,
    fontWeight: '500',
  },
  actionsSection: {
    padding: SIZES.paddingLarge,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SIZES.paddingMedium,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  actionText: {
    flex: 1,
    fontSize: FONTS.body2,
    color: COLORS.textPrimary,
    marginLeft: SIZES.marginMedium,
  },
  logoutButton: {
    marginHorizontal: SIZES.marginLarge,
    marginTop: SIZES.marginLarge,
  },
});

export default ProfileScreen;

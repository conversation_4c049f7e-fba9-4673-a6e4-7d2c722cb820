# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 41ms
  generate-prefab-packages
    [gap of 68ms]
    exec-prefab 978ms
    [gap of 140ms]
  generate-prefab-packages completed in 1186ms
  execute-generate-process
    exec-configure 1501ms
    [gap of 236ms]
  execute-generate-process completed in 1738ms
  [gap of 44ms]
  remove-unexpected-so-files 14ms
  [gap of 53ms]
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 3168ms


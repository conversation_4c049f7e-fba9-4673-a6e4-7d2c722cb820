import messaging from '@react-native-firebase/messaging';
import { Alert, Platform } from 'react-native';
import { getUserProfile, updateUserProfile } from './databaseService';

class PushNotificationService {
  constructor() {
    this.isInitialized = false;
    this.fcmToken = null;
  }

  /**
   * Initialize push notifications
   */
  async initialize(userId) {
    try {
      console.log('PushNotificationService: Initializing...');
      
      // Request permission for notifications
      const permission = await this.requestPermission();
      if (!permission) {
        console.log('PushNotificationService: Permission denied');
        return false;
      }

      // Get FCM token
      await this.getFCMToken(userId);

      // Set up message handlers
      this.setupMessageHandlers();

      this.isInitialized = true;
      console.log('PushNotificationService: Initialized successfully');
      return true;
    } catch (error) {
      console.error('PushNotificationService: Initialization failed:', error);
      return false;
    }
  }

  /**
   * Request notification permission
   */
  async requestPermission() {
    try {
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        console.log('PushNotificationService: Permission granted');
        return true;
      } else {
        console.log('PushNotificationService: Permission denied');
        return false;
      }
    } catch (error) {
      console.error('PushNotificationService: Permission request failed:', error);
      return false;
    }
  }

  /**
   * Get FCM token and save to user profile
   */
  async getFCMToken(userId) {
    try {
      const token = await messaging().getToken();
      if (token) {
        this.fcmToken = token;
        console.log('PushNotificationService: FCM Token:', token);

        // Save token to user profile for server-side notifications
        if (userId) {
          await this.saveFCMTokenToProfile(userId, token);
        }

        return token;
      } else {
        console.log('PushNotificationService: No FCM token available');
        return null;
      }
    } catch (error) {
      console.error('PushNotificationService: Failed to get FCM token:', error);
      return null;
    }
  }

  /**
   * Save FCM token to user profile
   */
  async saveFCMTokenToProfile(userId, token) {
    try {
      const userProfile = await getUserProfile(userId);
      const updatedProfile = {
        ...userProfile,
        fcmToken: token,
        fcmTokenUpdatedAt: new Date().toISOString(),
        deviceInfo: {
          platform: Platform.OS,
          version: Platform.Version,
        },
      };

      await updateUserProfile(userId, updatedProfile);
      console.log('PushNotificationService: FCM token saved to profile');
    } catch (error) {
      console.error('PushNotificationService: Failed to save FCM token:', error);
    }
  }

  /**
   * Set up message handlers for different app states
   */
  setupMessageHandlers() {
    // Handle messages when app is in background
    messaging().setBackgroundMessageHandler(async (remoteMessage) => {
      console.log('PushNotificationService: Background message:', remoteMessage);
      this.handleBackgroundMessage(remoteMessage);
    });

    // Handle messages when app is in foreground
    messaging().onMessage(async (remoteMessage) => {
      console.log('PushNotificationService: Foreground message:', remoteMessage);
      this.handleForegroundMessage(remoteMessage);
    });

    // Handle notification opened app
    messaging().onNotificationOpenedApp((remoteMessage) => {
      console.log('PushNotificationService: Notification opened app:', remoteMessage);
      this.handleNotificationOpened(remoteMessage);
    });

    // Check if app was opened from a notification (app was closed)
    messaging()
      .getInitialNotification()
      .then((remoteMessage) => {
        if (remoteMessage) {
          console.log('PushNotificationService: App opened from notification:', remoteMessage);
          this.handleNotificationOpened(remoteMessage);
        }
      });

    // Handle token refresh
    messaging().onTokenRefresh((token) => {
      console.log('PushNotificationService: Token refreshed:', token);
      this.fcmToken = token;
      // Update token in user profile if needed
    });
  }

  /**
   * Handle background messages
   */
  handleBackgroundMessage(remoteMessage) {
    console.log('PushNotificationService: Processing background message');
    
    // Extract custom data
    const { data } = remoteMessage;
    if (data?.type === 'plant_alert') {
      // Handle plant alert in background
      this.processPlantAlert(data);
    }
  }

  /**
   * Handle foreground messages (show in-app notification)
   */
  handleForegroundMessage(remoteMessage) {
    const { notification, data } = remoteMessage;
    
    // Show in-app alert when app is active
    Alert.alert(
      notification?.title || 'Plant Alert',
      notification?.body || 'Your plant needs attention!',
      [
        { text: 'Dismiss', style: 'cancel' },
        { 
          text: 'View Plant', 
          onPress: () => this.handleNotificationOpened(remoteMessage) 
        },
      ]
    );
  }

  /**
   * Handle notification tap (navigate to relevant screen)
   */
  handleNotificationOpened(remoteMessage) {
    const { data } = remoteMessage;
    
    if (data?.type === 'plant_alert') {
      // Navigate to plant details or specific plant
      this.navigateToPlant(data);
    }
  }

  /**
   * Process plant alert data
   */
  processPlantAlert(data) {
    const { plantId, alertType, severity } = data;
    
    console.log('PushNotificationService: Processing plant alert:', {
      plantId,
      alertType,
      severity,
    });

    // You can add additional processing here
    // e.g., update local storage, trigger local actions, etc.
  }

  /**
   * Navigate to plant screen
   */
  navigateToPlant(data) {
    // This will be implemented with navigation service
    console.log('PushNotificationService: Navigate to plant:', data.plantId);
    
    // For now, just log the action
    // In a real implementation, you'd use navigation service:
    // NavigationService.navigate('PlantDetails', { plantId: data.plantId });
  }

  /**
   * Subscribe to topic for plant alerts
   */
  async subscribeToPlantAlerts(userId) {
    try {
      const topic = `plant_alerts_${userId}`;
      await messaging().subscribeToTopic(topic);
      console.log(`PushNotificationService: Subscribed to topic: ${topic}`);
      return true;
    } catch (error) {
      console.error('PushNotificationService: Failed to subscribe to topic:', error);
      return false;
    }
  }

  /**
   * Unsubscribe from plant alerts
   */
  async unsubscribeFromPlantAlerts(userId) {
    try {
      const topic = `plant_alerts_${userId}`;
      await messaging().unsubscribeFromTopic(topic);
      console.log(`PushNotificationService: Unsubscribed from topic: ${topic}`);
      return true;
    } catch (error) {
      console.error('PushNotificationService: Failed to unsubscribe from topic:', error);
      return false;
    }
  }

  /**
   * Get current FCM token
   */
  getCurrentToken() {
    return this.fcmToken;
  }

  /**
   * Check if notifications are enabled
   */
  async checkPermission() {
    const authStatus = await messaging().hasPermission();
    return authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
           authStatus === messaging.AuthorizationStatus.PROVISIONAL;
  }
}

// Export singleton instance
export default new PushNotificationService();

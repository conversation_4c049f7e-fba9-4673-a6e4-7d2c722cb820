import React, { createContext, useState, useEffect, useContext } from 'react';
import { subscribeAuthState } from '../services/authService';
import { getUserProfile } from '../services/databaseService';

// Create the auth context
const AuthContext = createContext();

// Custom hook to use the auth context
export const useAuth = () => {
  return useContext(AuthContext);
};

// Auth provider component
export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    console.log('AuthContext: Setting up auth state listener');

    // Add a small delay to ensure Firebase is fully initialized
    const initializeAuth = async () => {
      try {
        // Wait a bit for Firebase to be ready
        await new Promise(resolve => setTimeout(resolve, 100));

        // Subscribe to auth state changes
        const unsubscribe = await subscribeAuthState(async (user) => {
          console.log('AuthContext: Auth state changed:', user ? 'User logged in' : 'User logged out');
          setCurrentUser(user);

          if (user) {
            // Fetch user profile from Firestore
            try {
              const profile = await getUserProfile(user.uid);
              setUserProfile(profile);
              console.log('AuthContext: User profile loaded');
            } catch (error) {
              console.error('Error fetching user profile:', error);
            }
          } else {
            setUserProfile(null);
          }

          setLoading(false);
        });

        return unsubscribe;
      } catch (error) {
        console.error('AuthContext: Error setting up auth listener:', error);
        setLoading(false);
        return () => {}; // Return empty unsubscribe function
      }
    };

    let unsubscribe = () => {};
    initializeAuth().then(unsub => {
      if (typeof unsub === 'function') {
        unsubscribe = unsub;
      }
    }).catch(error => {
      console.error('AuthContext: Failed to initialize auth:', error);
      setLoading(false);
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, []);

  // Update user profile in context when it changes
  const updateProfile = (newProfileData) => {
    setUserProfile(prevProfile => ({
      ...prevProfile,
      ...newProfileData
    }));
  };

  // Value to be provided to consumers
  const value = {
    currentUser,
    userProfile,
    updateProfile,
    isAuthenticated: !!currentUser,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};

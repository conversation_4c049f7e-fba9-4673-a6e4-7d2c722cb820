import React, { createContext, useState, useEffect, useContext } from 'react';
import { subscribeAuthState, logoutUser } from '../services/authService';
import { getUserProfile } from '../services/databaseService';

// Create the auth context
const AuthContext = createContext();

// Custom hook to use the auth context
export const useAuth = () => {
  return useContext(AuthContext);
};

// Auth provider component
export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    console.log('AuthContext: Setting up auth state listener');

    // Add a small delay to ensure Firebase is fully initialized
    const initializeAuth = async () => {
      try {
        // Wait a bit for Firebase to be ready
        await new Promise(resolve => setTimeout(resolve, 100));

        console.log('AuthContext: Subscribing to auth state changes...');

        // Subscribe to auth state changes
        const unsubscribe = await subscribeAuthState(async (user) => {
          console.log('AuthContext: Auth state changed:', user ? `User logged in (${user.email})` : 'User logged out');

          if (user) {
            console.log('AuthContext: User details:', {
              uid: user.uid,
              email: user.email,
              displayName: user.displayName,
              emailVerified: user.emailVerified
            });
          }

          setCurrentUser(user);

          if (user) {
            // Fetch user profile from Firestore
            try {
              console.log('AuthContext: Fetching user profile...');
              const profile = await getUserProfile(user.uid);
              setUserProfile(profile);
              console.log('AuthContext: User profile loaded successfully');
            } catch (error) {
              console.error('AuthContext: Error fetching user profile:', error);
              // Set a basic profile if Firestore fetch fails
              setUserProfile({
                displayName: user.displayName || 'User',
                email: user.email,
                photoURL: user.photoURL
              });
            }
          } else {
            setUserProfile(null);
          }

          setLoading(false);
          console.log('AuthContext: Auth initialization complete');
        });

        return unsubscribe;
      } catch (error) {
        console.error('AuthContext: Error setting up auth listener:', error);
        setLoading(false);
        return () => {}; // Return empty unsubscribe function
      }
    };

    let unsubscribe = () => {};
    initializeAuth().then(unsub => {
      if (typeof unsub === 'function') {
        unsubscribe = unsub;
      }
    }).catch(error => {
      console.error('AuthContext: Failed to initialize auth:', error);
      setLoading(false);
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, []);

  // Update user profile in context when it changes
  const updateProfile = (newProfileData) => {
    setUserProfile(prevProfile => ({
      ...prevProfile,
      ...newProfileData
    }));
  };

  // Logout function
  const logout = async () => {
    try {
      console.log('AuthContext: Logging out user...');
      await logoutUser();
      console.log('AuthContext: User logged out successfully');
    } catch (error) {
      console.error('AuthContext: Error logging out:', error);
      throw error;
    }
  };

  // Value to be provided to consumers
  const value = {
    currentUser,
    userProfile,
    updateProfile,
    logout,
    isAuthenticated: !!currentUser,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};

{"logs": [{"outputFile": "com.hydraiot.app-mergeDebugResources-51:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3135f157d17fbe3966fe84b667792db9\\transformed\\core-1.13.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "37,38,39,40,41,42,43,140", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3336,3430,3532,3629,3730,3837,3944,12788", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "3425,3527,3624,3725,3832,3939,4054,12884"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79f6ab95733be72bb6af37d95e29f537\\transformed\\browser-1.4.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,270,387", "endColumns": "113,100,116,102", "endOffsets": "164,265,382,485"}, "to": {"startLines": "66,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "6717,6991,7092,7209", "endColumns": "113,100,116,102", "endOffsets": "6826,7087,7204,7307"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af63e50a53e992ea9c751fc2deb2fc57\\transformed\\appcompat-1.7.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "203,302,410,500,605,722,805,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1906,2007,2115,2214,2321,2433,2537,2699,2796,2879"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,131", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "264,367,466,574,664,769,886,969,1051,1142,1235,1330,1424,1524,1617,1712,1806,1897,1988,2070,2171,2279,2378,2485,2597,2701,2863,12048", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "362,461,569,659,764,881,964,1046,1137,1230,1325,1419,1519,1612,1707,1801,1892,1983,2065,2166,2274,2373,2480,2592,2696,2858,2955,12126"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0a1c3fd62b5fb636be47a17086a4f32c\\transformed\\play-services-basement-18.5.0\\res\\values-sw\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5471", "endColumns": "145", "endOffsets": "5612"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7312d9ce0e64b42e563cee99ce0c7cbc\\transformed\\play-services-base-18.5.0\\res\\values-sw\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,445,566,671,830,951,1066,1176,1337,1439,1589,1712,1858,2013,2077,2148", "endColumns": "99,151,120,104,158,120,114,109,160,101,149,122,145,154,63,70,91", "endOffsets": "292,444,565,670,829,950,1065,1175,1336,1438,1588,1711,1857,2012,2076,2147,2239"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4456,4560,4716,4841,4950,5113,5238,5357,5617,5782,5888,6042,6169,6319,6478,6546,6621", "endColumns": "103,155,124,108,162,124,118,113,164,105,153,126,149,158,67,74,95", "endOffsets": "4555,4711,4836,4945,5108,5233,5352,5466,5777,5883,6037,6164,6314,6473,6541,6616,6712"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa0e231f4d27dc8a031f0119595b6f1\\transformed\\material-1.6.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,287,386,515,598,666,758,831,894,980,1043,1108,1176,1239,1293,1425,1482,1544,1598,1672,1810,1891,1971,2073,2158,2245,2333,2400,2466,2538,2620,2710,2782,2857,2928,3001,3098,3172,3267,3364,3438,3523,3623,3676,3744,3832,3922,3984,4048,4111,4228,4338,4449,4561", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,72,98,128,82,67,91,72,62,85,62,64,67,62,53,131,56,61,53,73,137,80,79,101,84,86,87,66,65,71,81,89,71,74,70,72,96,73,94,96,73,84,99,52,67,87,89,61,63,62,116,109,110,111,80", "endOffsets": "209,282,381,510,593,661,753,826,889,975,1038,1103,1171,1234,1288,1420,1477,1539,1593,1667,1805,1886,1966,2068,2153,2240,2328,2395,2461,2533,2615,2705,2777,2852,2923,2996,3093,3167,3262,3359,3433,3518,3618,3671,3739,3827,3917,3979,4043,4106,4223,4333,4444,4556,4637"}, "to": {"startLines": "2,36,44,45,46,67,68,73,76,78,79,80,81,82,83,84,85,86,87,88,89,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3263,4059,4158,4287,6831,6899,7383,7608,7740,7826,7889,7954,8022,8085,8139,8271,8328,8390,8444,8518,8888,8969,9049,9151,9236,9323,9411,9478,9544,9616,9698,9788,9860,9935,10006,10079,10176,10250,10345,10442,10516,10601,10701,10754,10822,10910,11000,11062,11126,11189,11306,11416,11527,11639", "endLines": "5,36,44,45,46,67,68,73,76,78,79,80,81,82,83,84,85,86,87,88,89,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126", "endColumns": "12,72,98,128,82,67,91,72,62,85,62,64,67,62,53,131,56,61,53,73,137,80,79,101,84,86,87,66,65,71,81,89,71,74,70,72,96,73,94,96,73,84,99,52,67,87,89,61,63,62,116,109,110,111,80", "endOffsets": "259,3331,4153,4282,4365,6894,6986,7451,7666,7821,7884,7949,8017,8080,8134,8266,8323,8385,8439,8513,8651,8964,9044,9146,9231,9318,9406,9473,9539,9611,9693,9783,9855,9930,10001,10074,10171,10245,10340,10437,10511,10596,10696,10749,10817,10905,10995,11057,11121,11184,11301,11411,11522,11634,11715"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ff5b0ce559ae890269420b3dec0e4060\\transformed\\react-android-0.79.2-debug\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,210,281,350,433,502,570,649,734,817,900,972,1062,1152,1231,1314,1398,1480,1556,1632,1719,1794,1877,1952", "endColumns": "68,85,70,68,82,68,67,78,84,82,82,71,89,89,78,82,83,81,75,75,86,74,82,74,77", "endOffsets": "119,205,276,345,428,497,565,644,729,812,895,967,1057,1147,1226,1309,1393,1475,1551,1627,1714,1789,1872,1947,2025"}, "to": {"startLines": "33,47,72,74,75,77,90,91,92,127,128,129,130,132,133,134,135,136,137,138,139,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2960,4370,7312,7456,7525,7671,8656,8724,8803,11720,11803,11886,11958,12131,12221,12300,12383,12467,12549,12625,12701,12889,12964,13047,13122", "endColumns": "68,85,70,68,82,68,67,78,84,82,82,71,89,89,78,82,83,81,75,75,86,74,82,74,77", "endOffsets": "3024,4451,7378,7520,7603,7735,8719,8798,8883,11798,11881,11953,12043,12216,12295,12378,12462,12544,12620,12696,12783,12959,13042,13117,13195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd1664c1c7ce01ff711fc15460fc5485\\transformed\\credentials-1.2.0-rc01\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,121", "endOffsets": "162,284"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3029,3141", "endColumns": "111,121", "endOffsets": "3136,3258"}}]}]}
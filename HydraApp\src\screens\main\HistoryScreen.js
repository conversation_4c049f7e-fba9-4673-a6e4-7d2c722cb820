import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  ActivityIndicator,
  Modal,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, SIZES, FONTS, SHADOWS } from '../../constants/theme';
import { useAuth } from '../../context/AuthContext';
import { getUserPlants, subscribeToWateringHistory } from '../../services/databaseService';
import moment from 'moment';

const HistoryScreen = ({ route, navigation }) => {
  const { currentUser } = useAuth();
  const [plants, setPlants] = useState([]);
  const [selectedPlant, setSelectedPlant] = useState(null);
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [historyLoading, setHistoryLoading] = useState(false);

  // Filter states
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    dateRange: 'week', // 'day', 'week', 'month', 'all'
    method: null, // null, 'manual', 'automatic', 'interval'
    limit: 50,
  });

  // Load user plants on component mount
  useEffect(() => {
    loadUserPlants();
  }, [currentUser]);

  // Subscribe to watering history when plant or filters change
  useEffect(() => {
    if (selectedPlant && currentUser) {
      loadWateringHistory();
    }
  }, [selectedPlant, filters, currentUser]);

  const loadUserPlants = async () => {
    if (!currentUser) return;

    try {
      setLoading(true);
      const userPlants = await getUserPlants(currentUser.uid);
      setPlants(userPlants);

      // Select first plant by default or from route params
      const targetPlantId = route?.params?.plantId;
      const targetPlant = targetPlantId
        ? userPlants.find(p => p.id === targetPlantId)
        : userPlants[0];

      if (targetPlant) {
        setSelectedPlant(targetPlant);
      }
    } catch (error) {
      console.error('Error loading user plants:', error);
      setPlants([]);
    } finally {
      setLoading(false);
    }
  };

  const loadWateringHistory = () => {
    if (!selectedPlant || !currentUser) return;

    setHistoryLoading(true);

    const historyFilters = {
      ...filters,
      plantId: selectedPlant.id,
    };

    const unsubscribe = subscribeToWateringHistory(
      currentUser.uid,
      historyFilters,
      (data) => {
        setHistory(data);
        setHistoryLoading(false);
      }
    );

    return () => unsubscribe();
  };

  // Filter functions
  const updateFilter = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const clearFilters = () => {
    setFilters({
      dateRange: 'week',
      method: null,
      limit: 50,
    });
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    return moment(timestamp).format('MMM DD, YYYY - h:mm A');
  };

  // Get method icon and color
  const getMethodInfo = (method) => {
    switch (method) {
      case 'manual':
        return { icon: 'hand-water', color: COLORS.primary };
      case 'automatic':
        return { icon: 'robot', color: COLORS.secondary };
      case 'interval':
        return { icon: 'clock-outline', color: COLORS.accent };
      default:
        return { icon: 'water-pump', color: COLORS.gray500 };
    }
  };

  // Render history item
  const renderHistoryItem = ({ item }) => {
    const methodInfo = getMethodInfo(item.method);

    return (
      <View style={styles.historyItem}>
        <View style={[styles.historyIconContainer, { backgroundColor: methodInfo.color + '20' }]}>
          <Icon
            name={methodInfo.icon}
            size={24}
            color={methodInfo.color}
          />
        </View>

        <View style={styles.historyContent}>
          <View style={styles.historyHeader}>
            <Text style={styles.historyTitle}>
              {item.method === 'manual' ? 'Manual Watering' :
               item.method === 'automatic' ? 'Condition-Based Watering' :
               item.method === 'interval' ? 'Interval Watering' : 'Watering'}
            </Text>
            <View style={[styles.methodBadge, { backgroundColor: methodInfo.color }]}>
              <Text style={styles.methodBadgeText}>
                {item.method?.toUpperCase() || 'UNKNOWN'}
              </Text>
            </View>
          </View>

          <Text style={styles.historyTimestamp}>
            {formatTimestamp(item.timestamp)}
          </Text>

          <View style={styles.historyDetails}>
            <Text style={styles.historyDetailText}>
              Duration: {item.duration || 'N/A'} seconds
            </Text>
            {item.potId && (
              <Text style={styles.historyDetailText}>
                Pot: {item.potId}
              </Text>
            )}
          </View>
        </View>
      </View>
    );
  };

  // Generate empty state
  const renderEmptyState = () => {
    if (historyLoading) {
      return (
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.emptyText}>Loading history...</Text>
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <Icon name="water-off" size={48} color={COLORS.gray400} />
        <Text style={styles.emptyTitle}>No Watering History</Text>
        <Text style={styles.emptyText}>
          No watering records found for the selected filters.
        </Text>
        <TouchableOpacity
          style={styles.clearFiltersButton}
          onPress={clearFilters}
        >
          <Text style={styles.clearFiltersText}>Clear Filters</Text>
        </TouchableOpacity>
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar backgroundColor={COLORS.background} barStyle="dark-content" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Loading your plants...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (plants.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar backgroundColor={COLORS.background} barStyle="dark-content" />
        <View style={styles.emptyContainer}>
          <Icon name="sprout-outline" size={64} color={COLORS.gray400} />
          <Text style={styles.emptyTitle}>No Plants Found</Text>
          <Text style={styles.emptyText}>
            Add plants to view their watering history
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={COLORS.background} barStyle="dark-content" />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>Watering History</Text>
        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setShowFilters(true)}
        >
          <Icon name="filter-variant" size={SIZES.iconMedium} color={COLORS.primary} />
          <Text style={styles.filterButtonText}>Filters</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.plantSelector}>
        <Text style={styles.sectionTitle}>Select Plant</Text>
        <FlatList
          horizontal
          data={plants}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[
                styles.plantItem,
                selectedPlant?.id === item.id && styles.selectedPlantItem,
              ]}
              onPress={() => setSelectedPlant(item)}
            >
              <Text
                style={[
                  styles.plantItemText,
                  selectedPlant?.id === item.id && styles.selectedPlantItemText,
                ]}
              >
                {item.name}
              </Text>
              {item.potId && (
                <Text style={[
                  styles.plantPotText,
                  selectedPlant?.id === item.id && styles.selectedPlantPotText,
                ]}>
                  {item.potId}
                </Text>
              )}
            </TouchableOpacity>
          )}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.plantSelectorContent}
        />
      </View>

      <View style={styles.historyContainer}>
        <View style={styles.historyHeader}>
          <Text style={styles.historyHeaderTitle}>
            {selectedPlant?.name || 'Select a Plant'} - Watering Events
          </Text>
          <Text style={styles.historySubtitle}>
            {filters.dateRange === 'all' ? 'All time' : `Last ${filters.dateRange}`}
            {filters.method && ` • ${filters.method} only`}
          </Text>
        </View>

        <FlatList
          data={history}
          keyExtractor={(item) => item.id}
          renderItem={renderHistoryItem}
          contentContainerStyle={styles.historyList}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={renderEmptyState}
          refreshing={historyLoading}
          onRefresh={() => loadWateringHistory()}
        />
      </View>

      {/* Filter Modal */}
      <Modal
        visible={showFilters}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowFilters(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filter History</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowFilters(false)}
              >
                <Icon name="close" size={SIZES.iconMedium} color={COLORS.gray600} />
              </TouchableOpacity>
            </View>

            {/* Date Range Filter */}
            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>Time Range</Text>
              <View style={styles.filterOptions}>
                {['day', 'week', 'month', 'all'].map((range) => (
                  <TouchableOpacity
                    key={range}
                    style={[
                      styles.filterOption,
                      filters.dateRange === range && styles.filterOptionSelected
                    ]}
                    onPress={() => updateFilter('dateRange', range)}
                  >
                    <Text style={[
                      styles.filterOptionText,
                      filters.dateRange === range && styles.filterOptionTextSelected
                    ]}>
                      {range === 'all' ? 'All Time' : `Last ${range}`}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Method Filter */}
            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>Watering Method</Text>
              <View style={styles.filterOptions}>
                {[null, 'manual', 'automatic', 'interval'].map((method) => (
                  <TouchableOpacity
                    key={method || 'all'}
                    style={[
                      styles.filterOption,
                      filters.method === method && styles.filterOptionSelected
                    ]}
                    onPress={() => updateFilter('method', method)}
                  >
                    <Text style={[
                      styles.filterOptionText,
                      filters.method === method && styles.filterOptionTextSelected
                    ]}>
                      {method ? method.charAt(0).toUpperCase() + method.slice(1) : 'All Methods'}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Modal Actions */}
            <View style={styles.modalActions}>
              <TouchableOpacity
                style={styles.clearButton}
                onPress={clearFilters}
              >
                <Text style={styles.clearButtonText}>Clear All</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.applyButton}
                onPress={() => setShowFilters(false)}
              >
                <Text style={styles.applyButtonText}>Apply Filters</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SIZES.paddingLarge,
    paddingVertical: SIZES.paddingMedium,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  headerTitle: {
    fontSize: FONTS.h3,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SIZES.paddingMedium,
    paddingVertical: SIZES.paddingSmall,
    backgroundColor: COLORS.primaryLightest,
    borderRadius: SIZES.radiusMedium,
  },
  filterButtonText: {
    fontSize: FONTS.body3,
    color: COLORS.primary,
    marginLeft: SIZES.marginXS,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SIZES.paddingLarge,
  },
  loadingText: {
    fontSize: FONTS.body1,
    color: COLORS.textSecondary,
    marginTop: SIZES.marginMedium,
  },
  plantSelector: {
    paddingHorizontal: SIZES.paddingLarge,
    paddingTop: SIZES.paddingMedium,
  },
  sectionTitle: {
    fontSize: FONTS.h4,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SIZES.marginMedium,
  },
  plantSelectorContent: {
    paddingBottom: SIZES.paddingSmall,
  },
  plantItem: {
    paddingHorizontal: SIZES.paddingMedium,
    paddingVertical: SIZES.paddingSmall,
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radiusMedium,
    marginRight: SIZES.marginMedium,
    ...SHADOWS.small,
  },
  selectedPlantItem: {
    backgroundColor: COLORS.primary,
  },
  plantItemText: {
    fontSize: FONTS.body2,
    color: COLORS.textPrimary,
  },
  selectedPlantItemText: {
    color: COLORS.white,
    fontWeight: '600',
  },
  plantPotText: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    marginTop: 2,
  },
  selectedPlantPotText: {
    color: COLORS.white,
    opacity: 0.9,
  },
  historyContainer: {
    flex: 1,
    marginTop: SIZES.marginMedium,
    paddingHorizontal: SIZES.paddingLarge,
  },
  historyHeader: {
    marginBottom: SIZES.marginMedium,
  },
  historyHeaderTitle: {
    fontSize: FONTS.h4,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  historySubtitle: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    marginTop: SIZES.marginXS,
  },
  historyList: {
    flexGrow: 1,
    paddingBottom: SIZES.paddingLarge,
  },
  historyItem: {
    flexDirection: 'row',
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radiusMedium,
    padding: SIZES.paddingMedium,
    marginBottom: SIZES.marginMedium,
    ...SHADOWS.small,
  },
  historyIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SIZES.marginMedium,
  },
  historyContent: {
    flex: 1,
    justifyContent: 'center',
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  historyTitle: {
    fontSize: FONTS.h5,
    fontWeight: '600',
    color: COLORS.textPrimary,
    flex: 1,
  },
  methodBadge: {
    paddingHorizontal: SIZES.paddingSmall,
    paddingVertical: 2,
    borderRadius: SIZES.radiusSmall,
  },
  methodBadgeText: {
    fontSize: FONTS.caption,
    fontWeight: '600',
    color: COLORS.white,
  },
  historyTimestamp: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    marginBottom: 6,
  },
  historyDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  historyDetailText: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SIZES.paddingLarge * 2,
  },
  emptyTitle: {
    fontSize: FONTS.h4,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginTop: SIZES.marginMedium,
    marginBottom: SIZES.marginSmall,
  },
  emptyText: {
    fontSize: FONTS.body2,
    color: COLORS.textSecondary,
    textAlign: 'center',
    paddingHorizontal: SIZES.paddingLarge,
    marginBottom: SIZES.marginMedium,
  },
  clearFiltersButton: {
    paddingHorizontal: SIZES.paddingLarge,
    paddingVertical: SIZES.paddingMedium,
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radiusMedium,
  },
  clearFiltersText: {
    fontSize: FONTS.body1,
    fontWeight: '600',
    color: COLORS.white,
  },
  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: SIZES.radiusLarge,
    borderTopRightRadius: SIZES.radiusLarge,
    paddingHorizontal: SIZES.paddingLarge,
    paddingBottom: SIZES.paddingLarge,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SIZES.paddingLarge,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray200,
  },
  modalTitle: {
    fontSize: FONTS.h3,
    fontWeight: '700',
    color: COLORS.textPrimary,
  },
  modalCloseButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.gray200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterSection: {
    marginVertical: SIZES.marginLarge,
  },
  filterSectionTitle: {
    fontSize: FONTS.h4,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SIZES.marginMedium,
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SIZES.marginSmall,
  },
  filterOption: {
    paddingHorizontal: SIZES.paddingMedium,
    paddingVertical: SIZES.paddingSmall,
    borderRadius: SIZES.radiusMedium,
    borderWidth: 1,
    borderColor: COLORS.gray300,
    backgroundColor: COLORS.white,
  },
  filterOptionSelected: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  filterOptionText: {
    fontSize: FONTS.body2,
    color: COLORS.textPrimary,
    fontWeight: '500',
  },
  filterOptionTextSelected: {
    color: COLORS.white,
  },
  modalActions: {
    flexDirection: 'row',
    gap: SIZES.marginMedium,
    marginTop: SIZES.marginLarge,
  },
  clearButton: {
    flex: 1,
    paddingVertical: SIZES.paddingMedium,
    borderRadius: SIZES.radiusMedium,
    backgroundColor: COLORS.gray200,
    alignItems: 'center',
  },
  clearButtonText: {
    fontSize: FONTS.body1,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  applyButton: {
    flex: 1,
    paddingVertical: SIZES.paddingMedium,
    borderRadius: SIZES.radiusMedium,
    backgroundColor: COLORS.primary,
    alignItems: 'center',
  },
  applyButtonText: {
    fontSize: FONTS.body1,
    fontWeight: '600',
    color: COLORS.white,
  },
});

export default HistoryScreen;

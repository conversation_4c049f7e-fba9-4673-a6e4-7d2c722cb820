// Sample plant data with optimal growing conditions
export const PLANTS = [
  {
    id: 'pot1',
    name: '<PERSON><PERSON>',
    imageUrl: null,
    imageBase64: null,
    optimalConditions: {
      soilMoisture: {
        min: 40,
        max: 70,
        ideal: 60,
      },
      temperature: {
        min: 18,
        max: 29,
        ideal: 24,
      },
      humidity: {
        min: 40,
        max: 80,
        ideal: 65,
      },
    },
    description: 'Tomatoes need consistent moisture but can suffer from fungal diseases if overwatered. They prefer warm temperatures and moderate humidity.',
    wateringTips: 'Water deeply but infrequently, allowing the top inch of soil to dry out between waterings.',
  },
  {
    id: 'pot2',
    name: '<PERSON>',
    imageUrl: null,
    imageBase64: null,
    optimalConditions: {
      soilMoisture: {
        min: 50,
        max: 70,
        ideal: 60,
      },
      temperature: {
        min: 18,
        max: 30,
        ideal: 24,
      },
      humidity: {
        min: 40,
        max: 60,
        ideal: 50,
      },
    },
    description: '<PERSON> loves warm conditions and consistent moisture. It will wilt quickly if allowed to dry out completely.',
    wateringTips: 'Keep soil consistently moist but not waterlogged. Water when the top inch of soil feels dry.',
  },
  {
    id: 'pot3',
    name: 'Succulent',
    imageUrl: null,
    imageBase64: null,
    optimalConditions: {
      soilMoisture: {
        min: 10,
        max: 30,
        ideal: 20,
      },
      temperature: {
        min: 10,
        max: 30,
        ideal: 22,
      },
      humidity: {
        min: 20,
        max: 50,
        ideal: 30,
      },
    },
    description: 'Succulents store water in their leaves and stems, making them drought-tolerant. They prefer dry conditions and will rot if overwatered.',
    wateringTips: 'Allow soil to dry completely between waterings. Water deeply but infrequently.',
  },
  {
    id: 'pot4',
    name: 'Fern',
    imageUrl: null,
    imageBase64: null,
    optimalConditions: {
      soilMoisture: {
        min: 60,
        max: 80,
        ideal: 70,
      },
      temperature: {
        min: 18,
        max: 24,
        ideal: 21,
      },
      humidity: {
        min: 50,
        max: 90,
        ideal: 70,
      },
    },
    description: 'Ferns love high humidity and consistently moist soil. They prefer indirect light and cooler temperatures.',
    wateringTips: 'Keep soil consistently moist. Mist regularly to increase humidity around the plant.',
  },
  {
    id: 'pot5',
    name: 'Orchid',
    imageUrl: null,
    imageBase64: null,
    optimalConditions: {
      soilMoisture: {
        min: 40,
        max: 60,
        ideal: 50,
      },
      temperature: {
        min: 18,
        max: 29,
        ideal: 24,
      },
      humidity: {
        min: 50,
        max: 70,
        ideal: 60,
      },
    },
    description: 'Orchids prefer bright, indirect light and good air circulation. They need specific watering cycles with periods of dryness.',
    wateringTips: 'Water thoroughly when the potting medium is nearly dry, then allow to drain completely.',
  },
];

// Threshold values for alerts
export const ALERT_THRESHOLDS = {
  SOIL_MOISTURE_LOW: 30,
  SOIL_MOISTURE_HIGH: 80,
  TEMPERATURE_LOW: 15,
  TEMPERATURE_HIGH: 32,
  HUMIDITY_LOW: 30,
  HUMIDITY_HIGH: 85,
};

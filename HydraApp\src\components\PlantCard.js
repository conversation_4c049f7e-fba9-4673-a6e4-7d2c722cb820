import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, SIZES, FONTS, SHADOWS } from '../constants/theme';
import { checkPlantNeedsWatering } from '../services/databaseService';
import PlantImage from './PlantImage';

const PlantCard = ({
  plant,
  sensorData,
  onPress,
  style,
}) => {
  const { name } = plant;
  const { soilMoisture, temperature, humidity } = sensorData || {
    soilMoisture: 0,
    temperature: 0,
    humidity: 0,
  };

  // Check if plant needs watering
  const needsWater = plant.conditions ?
    checkPlantNeedsWatering(sensorData, plant.conditions) :
    false;

  // Get status color
  const getStatusColor = () => {
    if (needsWater) {
      return COLORS.danger;
    }

    // If no conditions are set, show neutral status
    if (!plant.conditions) {
      return COLORS.gray500;
    }

    // Check if any sensor is outside optimal range
    const { conditions } = plant;
    const isSoilMoistureOptimal =
      soilMoisture >= conditions.soilMoisture.min &&
      soilMoisture <= conditions.soilMoisture.max;

    const isTemperatureOptimal =
      temperature >= conditions.temperature.min &&
      temperature <= conditions.temperature.max;

    const isHumidityOptimal =
      humidity >= conditions.humidity.min &&
      humidity <= conditions.humidity.max;

    if (!isSoilMoistureOptimal || !isTemperatureOptimal || !isHumidityOptimal) {
      return COLORS.warning;
    }

    return COLORS.success;
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={0.9}
    >
      <View style={styles.cardContent}>
          <View style={styles.imageContainer}>
            <PlantImage
              plantId={plant.id}
              imageUrl={plant.imageUrl}
              imageBase64={plant.imageBase64}
              plantType={plant.name}
              size="small"
              style={styles.image}
            />
            <View style={[styles.statusIndicator, { backgroundColor: getStatusColor() }]}>
              <View style={styles.statusDot} />
            </View>
          </View>

          <View style={styles.contentContainer}>
            <View style={styles.headerRow}>
              <Text style={styles.name}>{name}</Text>
              <View style={[styles.statusBadge, { backgroundColor: getStatusColor() }]}>
                <Text style={styles.statusBadgeText}>
                  {needsWater ? 'LOW' : 'OK'}
                </Text>
              </View>
            </View>

            <View style={styles.sensorRow}>
              <View style={styles.sensorItem}>
                <View style={[styles.sensorIconContainer, { backgroundColor: COLORS.primaryLightest }]}>
                  <Icon name="water-percent" size={SIZES.iconSmall} color={COLORS.primary} />
                </View>
                <Text style={styles.sensorText}>{soilMoisture}%</Text>
              </View>

              <View style={styles.sensorItem}>
                <View style={[styles.sensorIconContainer, { backgroundColor: COLORS.secondaryLightest }]}>
                  <Icon name="thermometer" size={SIZES.iconSmall} color={COLORS.secondary} />
                </View>
                <Text style={styles.sensorText}>{temperature}°C</Text>
              </View>

              <View style={styles.sensorItem}>
                <View style={[styles.sensorIconContainer, { backgroundColor: COLORS.primaryLightest }]}>
                  <Icon name="water" size={SIZES.iconSmall} color={COLORS.accent} />
                </View>
                <Text style={styles.sensorText}>{humidity}%</Text>
              </View>
            </View>

            {needsWater && (
              <View style={styles.warningContainer}>
                <Icon name="alert-circle" size={SIZES.iconSmall} color={COLORS.danger} />
                <Text style={styles.warningText}>Needs watering!</Text>
              </View>
            )}
          </View>

          <View style={styles.arrowContainer}>
            <View style={styles.arrowBackground}>
              <Icon name="chevron-right" size={SIZES.iconMedium} color={COLORS.primary} />
            </View>
          </View>
        </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SIZES.marginMedium,
    borderRadius: SIZES.radiusLarge,
    overflow: 'hidden',
    ...SHADOWS.medium,
  },
  cardContent: {
    flexDirection: 'row',
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radiusLarge,
    padding: SIZES.paddingMedium,
  },
  imageContainer: {
    position: 'relative',
    marginRight: SIZES.marginMedium,
  },
  image: {
    width: 70,
    height: 70,
    borderRadius: 35, // Make it circular (half of width/height)
  },
  statusIndicator: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: COLORS.white,
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.small,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: COLORS.white,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.marginSmall,
  },
  name: {
    fontSize: FONTS.h4,
    fontWeight: '700',
    color: COLORS.textPrimary,
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: SIZES.paddingSmall,
    paddingVertical: SIZES.paddingXS,
    borderRadius: SIZES.radiusSmall,
    marginLeft: SIZES.marginSmall,
  },
  statusBadgeText: {
    fontSize: FONTS.body3,
    fontWeight: '600',
    color: COLORS.white,
  },
  sensorRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SIZES.marginXS,
  },
  sensorItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  sensorIconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SIZES.marginXS,
  },
  sensorText: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: SIZES.marginXS,
    backgroundColor: COLORS.dangerLight + '15',
    paddingHorizontal: SIZES.paddingSmall,
    paddingVertical: SIZES.paddingXS,
    borderRadius: SIZES.radiusSmall,
  },
  warningText: {
    fontSize: FONTS.body3,
    color: COLORS.danger,
    marginLeft: SIZES.marginXS,
    fontWeight: '600',
  },
  arrowContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: SIZES.marginSmall,
  },
  arrowBackground: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.primaryLightest,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default PlantCard;

import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, SIZES, FONTS, SHADOWS } from '../constants/theme';
import PlantImage from './PlantImage';

const PlantCard = ({
  plant,
  sensorData,
  onPress,
  style,
}) => {
  const { name } = plant;
  const { soilMoisture, temperature, humidity } = sensorData || {
    soilMoisture: 0,
    temperature: 0,
    humidity: 0,
  };

  // State for alerts dropdown
  const [showAllAlerts, setShowAllAlerts] = useState(false);

  // Get comprehensive plant analysis
  const getPlantAnalysis = () => {
    // If no conditions are set, return neutral status
    if (!plant.conditions) {
      return {
        overallStatus: 'UNKNOWN',
        overallColor: COLORS.gray500,
        alerts: [],
        hasAlerts: false,
        primaryAlert: null
      };
    }

    const { conditions } = plant;
    const alerts = [];

    // Check soil moisture
    const soilTooLow = soilMoisture < conditions.soilMoisture.min;
    const soilTooHigh = soilMoisture > conditions.soilMoisture.max;
    const soilOptimal = soilMoisture >= conditions.soilMoisture.min && soilMoisture <= conditions.soilMoisture.max;

    // Check temperature
    const tempTooLow = temperature < conditions.temperature.min;
    const tempTooHigh = temperature > conditions.temperature.max;
    const tempOptimal = temperature >= conditions.temperature.min && temperature <= conditions.temperature.max;

    // Check humidity
    const humidityTooLow = humidity < conditions.humidity.min;
    const humidityTooHigh = humidity > conditions.humidity.max;
    const humidityOptimal = humidity >= conditions.humidity.min && humidity <= conditions.humidity.max;

    // Generate alerts for each sensor
    if (soilTooLow) {
      alerts.push({
        type: 'soil',
        severity: 'critical',
        icon: 'water-alert',
        message: `Needs watering! (${soilMoisture}% < ${conditions.soilMoisture.min}%)`,
        color: COLORS.danger
      });
    } else if (soilTooHigh) {
      alerts.push({
        type: 'soil',
        severity: 'warning',
        icon: 'water-off',
        message: `Too much water! (${soilMoisture}% > ${conditions.soilMoisture.max}%)`,
        color: COLORS.warning
      });
    }

    if (tempTooLow) {
      alerts.push({
        type: 'temperature',
        severity: 'warning',
        icon: 'thermometer-low',
        message: `Too cold! (${temperature}°C < ${conditions.temperature.min}°C)`,
        color: COLORS.info
      });
    } else if (tempTooHigh) {
      alerts.push({
        type: 'temperature',
        severity: 'warning',
        icon: 'thermometer-high',
        message: `Too hot! (${temperature}°C > ${conditions.temperature.max}°C)`,
        color: COLORS.warning
      });
    }

    if (humidityTooLow) {
      alerts.push({
        type: 'humidity',
        severity: 'warning',
        icon: 'air-humidifier-off',
        message: `Too dry! (${humidity}% < ${conditions.humidity.min}%)`,
        color: COLORS.secondary
      });
    } else if (humidityTooHigh) {
      alerts.push({
        type: 'humidity',
        severity: 'warning',
        icon: 'water-percent',
        message: `Too humid! (${humidity}% > ${conditions.humidity.max}%)`,
        color: COLORS.accent
      });
    }

    // Determine overall status based on all conditions
    let overallStatus = 'GOOD';
    let overallColor = COLORS.success;

    if (alerts.some(alert => alert.severity === 'critical')) {
      overallStatus = 'CRITICAL';
      overallColor = COLORS.danger;
    } else if (alerts.length > 0) {
      overallStatus = 'WARNING';
      overallColor = COLORS.warning;
    }

    // Primary alert is the most severe one (soil moisture takes priority)
    const primaryAlert = alerts.find(alert => alert.type === 'soil') || alerts[0] || null;

    return {
      overallStatus,
      overallColor,
      alerts,
      hasAlerts: alerts.length > 0,
      primaryAlert,
      soilOptimal,
      tempOptimal,
      humidityOptimal
    };
  };

  const plantAnalysis = getPlantAnalysis();

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={0.9}
    >
      <View style={styles.cardContent}>
          <View style={styles.imageContainer}>
            <PlantImage
              plantId={plant.id}
              imageUrl={plant.imageUrl}
              imageBase64={plant.imageBase64}
              plantType={plant.name}
              size="small"
              style={styles.image}
            />
            <View style={[styles.statusIndicator, { backgroundColor: plantAnalysis.overallColor }]}>
              <View style={styles.statusDot} />
            </View>
          </View>

          <View style={styles.contentContainer}>
            <View style={styles.headerRow}>
              <Text style={styles.name}>{name}</Text>
              <View style={[styles.statusBadge, { backgroundColor: plantAnalysis.overallColor }]}>
                <Text style={styles.statusBadgeText}>
                  {plantAnalysis.overallStatus}
                </Text>
              </View>
            </View>

            <View style={styles.sensorRow}>
              <View style={styles.sensorItem}>
                <View style={[styles.sensorIconContainer, { backgroundColor: COLORS.primaryLightest }]}>
                  <Icon name="water-percent" size={SIZES.iconSmall} color={COLORS.primary} />
                </View>
                <Text style={styles.sensorText}>{soilMoisture}%</Text>
              </View>

              <View style={styles.sensorItem}>
                <View style={[styles.sensorIconContainer, { backgroundColor: COLORS.secondaryLightest }]}>
                  <Icon name="thermometer" size={SIZES.iconSmall} color={COLORS.secondary} />
                </View>
                <Text style={styles.sensorText}>{temperature}°C</Text>
              </View>

              <View style={styles.sensorItem}>
                <View style={[styles.sensorIconContainer, { backgroundColor: COLORS.primaryLightest }]}>
                  <Icon name="water" size={SIZES.iconSmall} color={COLORS.accent} />
                </View>
                <Text style={styles.sensorText}>{humidity}%</Text>
              </View>
            </View>

            {plantAnalysis.hasAlerts && (
              <View style={styles.alertsContainer}>
                {/* Primary Alert (Always Visible) */}
                <TouchableOpacity
                  style={[styles.primaryAlert, { backgroundColor: plantAnalysis.primaryAlert.color + '15' }]}
                  onPress={() => setShowAllAlerts(!showAllAlerts)}
                  activeOpacity={0.7}
                >
                  <Icon
                    name={plantAnalysis.primaryAlert.icon}
                    size={SIZES.iconSmall}
                    color={plantAnalysis.primaryAlert.color}
                  />
                  <Text style={[styles.alertText, { color: plantAnalysis.primaryAlert.color }]}>
                    {plantAnalysis.primaryAlert.message}
                  </Text>
                  {plantAnalysis.alerts.length > 1 && (
                    <View style={styles.alertDropdownIndicator}>
                      <Icon
                        name={showAllAlerts ? "chevron-up" : "chevron-down"}
                        size={SIZES.iconSmall}
                        color={plantAnalysis.primaryAlert.color}
                      />
                      <Text style={[styles.alertCount, { color: plantAnalysis.primaryAlert.color }]}>
                        +{plantAnalysis.alerts.length - 1}
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>

                {/* Additional Alerts (Dropdown) */}
                {showAllAlerts && plantAnalysis.alerts.length > 1 && (
                  <View style={styles.additionalAlerts}>
                    {plantAnalysis.alerts.slice(1).map((alert, index) => (
                      <View
                        key={index}
                        style={[styles.additionalAlert, { backgroundColor: alert.color + '10' }]}
                      >
                        <Icon
                          name={alert.icon}
                          size={SIZES.iconSmall}
                          color={alert.color}
                        />
                        <Text style={[styles.alertText, { color: alert.color }]}>
                          {alert.message}
                        </Text>
                      </View>
                    ))}
                  </View>
                )}
              </View>
            )}
          </View>

          <View style={styles.arrowContainer}>
            <View style={styles.arrowBackground}>
              <Icon name="chevron-right" size={SIZES.iconMedium} color={COLORS.primary} />
            </View>
          </View>
        </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SIZES.marginMedium,
    borderRadius: SIZES.radiusLarge,
    overflow: 'hidden',
    ...SHADOWS.medium,
  },
  cardContent: {
    flexDirection: 'row',
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radiusLarge,
    padding: SIZES.paddingMedium,
  },
  imageContainer: {
    position: 'relative',
    marginRight: SIZES.marginMedium,
  },
  image: {
    width: 70,
    height: 70,
    borderRadius: 35, // Make it circular (half of width/height)
  },
  statusIndicator: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: COLORS.white,
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.small,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: COLORS.white,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.marginSmall,
  },
  name: {
    fontSize: FONTS.h4,
    fontWeight: '700',
    color: COLORS.textPrimary,
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: SIZES.paddingSmall,
    paddingVertical: SIZES.paddingXS,
    borderRadius: SIZES.radiusSmall,
    marginLeft: SIZES.marginSmall,
  },
  statusBadgeText: {
    fontSize: FONTS.body3,
    fontWeight: '600',
    color: COLORS.white,
  },
  sensorRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SIZES.marginXS,
  },
  sensorItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  sensorIconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SIZES.marginXS,
  },
  sensorText: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  alertsContainer: {
    marginTop: SIZES.marginXS,
  },
  primaryAlert: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SIZES.paddingSmall,
    paddingVertical: SIZES.paddingXS,
    borderRadius: SIZES.radiusSmall,
    marginBottom: SIZES.marginXS,
  },
  alertText: {
    fontSize: FONTS.body3,
    marginLeft: SIZES.marginXS,
    fontWeight: '600',
    flex: 1,
  },
  alertDropdownIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: SIZES.marginSmall,
  },
  alertCount: {
    fontSize: FONTS.body3,
    fontWeight: '600',
    marginLeft: 2,
  },
  additionalAlerts: {
    marginTop: SIZES.marginXS,
  },
  additionalAlert: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SIZES.paddingSmall,
    paddingVertical: SIZES.paddingXS,
    borderRadius: SIZES.radiusSmall,
    marginBottom: SIZES.marginXS,
    marginLeft: SIZES.marginMedium,
  },
  arrowContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: SIZES.marginSmall,
  },
  arrowBackground: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.primaryLightest,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default PlantCard;

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, SIZES, FONTS, SHADOWS } from '../constants/theme';
import PlantImage from './PlantImage';

const PlantCard = ({
  plant,
  sensorData,
  onPress,
  style,
}) => {
  const { name } = plant;
  const { soilMoisture, temperature, humidity } = sensorData || {
    soilMoisture: 0,
    temperature: 0,
    humidity: 0,
  };

  // Get plant status based on conditions
  const getPlantStatus = () => {
    // If no conditions are set, return neutral status
    if (!plant.conditions) {
      return {
        needsWater: false,
        needsAttention: false,
        status: 'UNKNOWN',
        color: COLORS.gray500
      };
    }

    const { conditions } = plant;

    // Check soil moisture specifically for watering needs
    const soilTooLow = soilMoisture < conditions.soilMoisture.min;
    const soilTooHigh = soilMoisture > conditions.soilMoisture.max;

    // Check other conditions for general plant health
    const tempTooLow = temperature < conditions.temperature.min;
    const tempTooHigh = temperature > conditions.temperature.max;
    const humidityTooLow = humidity < conditions.humidity.min;
    const humidityTooHigh = humidity > conditions.humidity.max;

    // Priority 1: Soil moisture issues
    if (soilTooLow) {
      return {
        needsWater: true,
        needsAttention: true,
        status: 'LOW',
        color: COLORS.danger
      };
    }

    if (soilTooHigh) {
      return {
        needsWater: false,
        needsAttention: true,
        status: 'HIGH',
        color: COLORS.warning
      };
    }

    // Priority 2: Temperature or humidity issues
    if (tempTooLow || tempTooHigh || humidityTooLow || humidityTooHigh) {
      return {
        needsWater: false,
        needsAttention: true,
        status: 'WARN',
        color: COLORS.warning
      };
    }

    // All conditions are optimal
    return {
      needsWater: false,
      needsAttention: false,
      status: 'OK',
      color: COLORS.success
    };
  };

  const plantStatus = getPlantStatus();

  // Get appropriate warning message based on status
  const getWarningMessage = (status, soil, temp, humid, conditions) => {
    if (!conditions) return 'No conditions set';

    if (status.needsWater) {
      return `Needs watering! (${soil}% < ${conditions.soilMoisture.min}%)`;
    }

    if (soil > conditions.soilMoisture.max) {
      return `Too much water! (${soil}% > ${conditions.soilMoisture.max}%)`;
    }

    if (temp < conditions.temperature.min) {
      return `Too cold! (${temp}°C < ${conditions.temperature.min}°C)`;
    }

    if (temp > conditions.temperature.max) {
      return `Too hot! (${temp}°C > ${conditions.temperature.max}°C)`;
    }

    if (humid < conditions.humidity.min) {
      return `Too dry! (${humid}% < ${conditions.humidity.min}%)`;
    }

    if (humid > conditions.humidity.max) {
      return `Too humid! (${humid}% > ${conditions.humidity.max}%)`;
    }

    return 'Needs attention';
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={0.9}
    >
      <View style={styles.cardContent}>
          <View style={styles.imageContainer}>
            <PlantImage
              plantId={plant.id}
              imageUrl={plant.imageUrl}
              imageBase64={plant.imageBase64}
              plantType={plant.name}
              size="small"
              style={styles.image}
            />
            <View style={[styles.statusIndicator, { backgroundColor: plantStatus.color }]}>
              <View style={styles.statusDot} />
            </View>
          </View>

          <View style={styles.contentContainer}>
            <View style={styles.headerRow}>
              <Text style={styles.name}>{name}</Text>
              <View style={[styles.statusBadge, { backgroundColor: plantStatus.color }]}>
                <Text style={styles.statusBadgeText}>
                  {plantStatus.status}
                </Text>
              </View>
            </View>

            <View style={styles.sensorRow}>
              <View style={styles.sensorItem}>
                <View style={[styles.sensorIconContainer, { backgroundColor: COLORS.primaryLightest }]}>
                  <Icon name="water-percent" size={SIZES.iconSmall} color={COLORS.primary} />
                </View>
                <Text style={styles.sensorText}>{soilMoisture}%</Text>
              </View>

              <View style={styles.sensorItem}>
                <View style={[styles.sensorIconContainer, { backgroundColor: COLORS.secondaryLightest }]}>
                  <Icon name="thermometer" size={SIZES.iconSmall} color={COLORS.secondary} />
                </View>
                <Text style={styles.sensorText}>{temperature}°C</Text>
              </View>

              <View style={styles.sensorItem}>
                <View style={[styles.sensorIconContainer, { backgroundColor: COLORS.primaryLightest }]}>
                  <Icon name="water" size={SIZES.iconSmall} color={COLORS.accent} />
                </View>
                <Text style={styles.sensorText}>{humidity}%</Text>
              </View>
            </View>

            {plantStatus.needsAttention && (
              <View style={styles.warningContainer}>
                <Icon
                  name={plantStatus.needsWater ? "water-alert" : "alert-circle"}
                  size={SIZES.iconSmall}
                  color={plantStatus.needsWater ? COLORS.danger : COLORS.warning}
                />
                <Text style={[
                  styles.warningText,
                  { color: plantStatus.needsWater ? COLORS.danger : COLORS.warning }
                ]}>
                  {getWarningMessage(plantStatus, soilMoisture, temperature, humidity, plant.conditions)}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.arrowContainer}>
            <View style={styles.arrowBackground}>
              <Icon name="chevron-right" size={SIZES.iconMedium} color={COLORS.primary} />
            </View>
          </View>
        </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SIZES.marginMedium,
    borderRadius: SIZES.radiusLarge,
    overflow: 'hidden',
    ...SHADOWS.medium,
  },
  cardContent: {
    flexDirection: 'row',
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radiusLarge,
    padding: SIZES.paddingMedium,
  },
  imageContainer: {
    position: 'relative',
    marginRight: SIZES.marginMedium,
  },
  image: {
    width: 70,
    height: 70,
    borderRadius: 35, // Make it circular (half of width/height)
  },
  statusIndicator: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: COLORS.white,
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.small,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: COLORS.white,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.marginSmall,
  },
  name: {
    fontSize: FONTS.h4,
    fontWeight: '700',
    color: COLORS.textPrimary,
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: SIZES.paddingSmall,
    paddingVertical: SIZES.paddingXS,
    borderRadius: SIZES.radiusSmall,
    marginLeft: SIZES.marginSmall,
  },
  statusBadgeText: {
    fontSize: FONTS.body3,
    fontWeight: '600',
    color: COLORS.white,
  },
  sensorRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SIZES.marginXS,
  },
  sensorItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  sensorIconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SIZES.marginXS,
  },
  sensorText: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: SIZES.marginXS,
    backgroundColor: COLORS.dangerLight + '15',
    paddingHorizontal: SIZES.paddingSmall,
    paddingVertical: SIZES.paddingXS,
    borderRadius: SIZES.radiusSmall,
  },
  warningText: {
    fontSize: FONTS.body3,
    color: COLORS.danger,
    marginLeft: SIZES.marginXS,
    fontWeight: '600',
  },
  arrowContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: SIZES.marginSmall,
  },
  arrowBackground: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.primaryLightest,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default PlantCard;

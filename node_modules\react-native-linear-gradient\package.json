{"name": "react-native-linear-gradient", "version": "2.8.3", "description": "A <LinearGradient> element for React Native", "main": "index.js", "types": "index.d.ts", "files": ["android", "ios", "windows", "common.js", "index.android.js", "index.d.ts", "index.ios.js", "index.windows.js", "BVLinearGradient.podspec"], "scripts": {"flow": "flow check"}, "repository": {"type": "git", "url": "https://github.com/react-native-linear-gradient/react-native-linear-gradient.git"}, "author": "<PERSON> <<EMAIL>> (https://github.com/brentvatne)", "contributors": ["<PERSON> <<EMAIL>> (https://github.com/dgladkov)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/friederbluemle)", "Gant Laborde <<EMAIL>> (https://github.com/gantman)", "<PERSON> <<EMAIL>> (https://github.com/nickhudkins)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/radex)"], "license": "MIT", "dependencies": {}, "devDependencies": {"@types/react-native": "^0.70.14", "flow-bin": "^0.182.0", "react": "18.1.0", "react-native": "0.70.12"}, "peerDependencies": {"react": "*", "react-native": "*"}}
{"logs": [{"outputFile": "com.hydraiot.app-mergeDebugResources-51:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af63e50a53e992ea9c751fc2deb2fc57\\transformed\\appcompat-1.7.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,131", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "273,381,481,590,676,781,899,985,1064,1155,1248,1343,1437,1531,1624,1720,1819,1910,2004,2084,2191,2292,2389,2495,2595,2693,2843,11803", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "376,476,585,671,776,894,980,1059,1150,1243,1338,1432,1526,1619,1715,1814,1905,1999,2079,2186,2287,2384,2490,2590,2688,2838,2938,11879"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3135f157d17fbe3966fe84b667792db9\\transformed\\core-1.13.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "37,38,39,40,41,42,43,140", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3326,3422,3524,3622,3727,3832,3944,12532", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "3417,3519,3617,3722,3827,3939,4055,12628"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0a1c3fd62b5fb636be47a17086a4f32c\\transformed\\play-services-basement-18.5.0\\res\\values-fi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5423", "endColumns": "149", "endOffsets": "5568"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa0e231f4d27dc8a031f0119595b6f1\\transformed\\material-1.6.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,223,306,402,510,594,659,752,827,892,980,1046,1104,1175,1241,1295,1405,1465,1529,1583,1656,1772,1856,1937,2040,2125,2210,2300,2367,2433,2510,2592,2676,2750,2829,2906,2978,3067,3143,3234,3329,3403,3476,3570,3624,3696,3782,3868,3930,3994,4057,4158,4260,4355,4458", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,95,107,83,64,92,74,64,87,65,57,70,65,53,109,59,63,53,72,115,83,80,102,84,84,89,66,65,76,81,83,73,78,76,71,88,75,90,94,73,72,93,53,71,85,85,61,63,62,100,101,94,102,78", "endOffsets": "218,301,397,505,589,654,747,822,887,975,1041,1099,1170,1236,1290,1400,1460,1524,1578,1651,1767,1851,1932,2035,2120,2205,2295,2362,2428,2505,2587,2671,2745,2824,2901,2973,3062,3138,3229,3324,3398,3471,3565,3619,3691,3777,3863,3925,3989,4052,4153,4255,4350,4453,4532"}, "to": {"startLines": "2,36,44,45,46,67,68,73,76,78,79,80,81,82,83,84,85,86,87,88,89,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3243,4060,4156,4264,6704,6769,7243,7469,7603,7691,7757,7815,7886,7952,8006,8116,8176,8240,8294,8367,8711,8795,8876,8979,9064,9149,9239,9306,9372,9449,9531,9615,9689,9768,9845,9917,10006,10082,10173,10268,10342,10415,10509,10563,10635,10721,10807,10869,10933,10996,11097,11199,11294,11397", "endLines": "5,36,44,45,46,67,68,73,76,78,79,80,81,82,83,84,85,86,87,88,89,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126", "endColumns": "12,82,95,107,83,64,92,74,64,87,65,57,70,65,53,109,59,63,53,72,115,83,80,102,84,84,89,66,65,76,81,83,73,78,76,71,88,75,90,94,73,72,93,53,71,85,85,61,63,62,100,101,94,102,78", "endOffsets": "268,3321,4151,4259,4343,6764,6857,7313,7529,7686,7752,7810,7881,7947,8001,8111,8171,8235,8289,8362,8478,8790,8871,8974,9059,9144,9234,9301,9367,9444,9526,9610,9684,9763,9840,9912,10001,10077,10168,10263,10337,10410,10504,10558,10630,10716,10802,10864,10928,10991,11092,11194,11289,11392,11471"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ff5b0ce559ae890269420b3dec0e4060\\transformed\\react-android-0.79.2-debug\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,208,280,348,431,500,570,649,728,813,899,973,1055,1139,1215,1300,1384,1464,1543,1618,1703,1779,1859,1930", "endColumns": "70,81,71,67,82,68,69,78,78,84,85,73,81,83,75,84,83,79,78,74,84,75,79,70,78", "endOffsets": "121,203,275,343,426,495,565,644,723,808,894,968,1050,1134,1210,1295,1379,1459,1538,1613,1698,1774,1854,1925,2004"}, "to": {"startLines": "33,47,72,74,75,77,90,91,92,127,128,129,130,132,133,134,135,136,137,138,139,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2943,4348,7171,7318,7386,7534,8483,8553,8632,11476,11561,11647,11721,11884,11968,12044,12129,12213,12293,12372,12447,12633,12709,12789,12860", "endColumns": "70,81,71,67,82,68,69,78,78,84,85,73,81,83,75,84,83,79,78,74,84,75,79,70,78", "endOffsets": "3009,4425,7238,7381,7464,7598,8548,8627,8706,11556,11642,11716,11798,11963,12039,12124,12208,12288,12367,12442,12527,12704,12784,12855,12934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd1664c1c7ce01ff711fc15460fc5485\\transformed\\credentials-1.2.0-rc01\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,116", "endOffsets": "162,279"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3014,3126", "endColumns": "111,116", "endOffsets": "3121,3238"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7312d9ce0e64b42e563cee99ce0c7cbc\\transformed\\play-services-base-18.5.0\\res\\values-fi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,449,576,678,817,939,1051,1154,1291,1393,1538,1660,1804,1939,2001,2067", "endColumns": "106,148,126,101,138,121,111,102,136,101,144,121,143,134,61,65,78", "endOffsets": "299,448,575,677,816,938,1050,1153,1290,1392,1537,1659,1803,1938,2000,2066,2145"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4430,4541,4694,4825,4931,5074,5200,5316,5573,5714,5820,5969,6095,6243,6382,6448,6518", "endColumns": "110,152,130,105,142,125,115,106,140,105,148,125,147,138,65,69,82", "endOffsets": "4536,4689,4820,4926,5069,5195,5311,5418,5709,5815,5964,6090,6238,6377,6443,6513,6596"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79f6ab95733be72bb6af37d95e29f537\\transformed\\browser-1.4.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,368", "endColumns": "102,100,108,98", "endOffsets": "153,254,363,462"}, "to": {"startLines": "66,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "6601,6862,6963,7072", "endColumns": "102,100,108,98", "endOffsets": "6699,6958,7067,7166"}}]}]}
# ninja log v5
72036	72286	7701142460529974	E:/HYDRA-IOT/HydraApp/android/app/build/intermediates/cxx/Debug/1n1k36bs/obj/armeabi-v7a/libreact_codegen_rnsvg.so	f63a3193ede139ff
23591	31068	7701142048194208	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o	8a57876fa6a31465
1	30	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/armeabi-v7a/CMakeFiles/cmake.verify_globs	105cfe73fb189e6d
7238	13581	7701141872794924	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	4167b1889af53135
28700	34813	7701142085468083	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b7046d699b6e0fe79e4beb14c8a51d24/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o	34b09533bb558245
13792	22249	7701141959992478	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/ComponentDescriptors.cpp.o	dc99a6b49cdb4d75
7056	13190	7701141868210341	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o	8538fbd21a5944cd
31468	40674	7701142143945691	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8311d7994bdb89babd489a4af328892e/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o	a45e4ccf2391d4ea
13191	21031	7701141947024489	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o	bcbc5e42265ab2cb
47248	57496	7701142312094270	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/598dc9a4de586092988288a9ed8b2a9c/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	45eb03638e553f12
21120	28472	7701142022258126	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o	4da9248a7071d993
41805	42913	7701142162308071	E:/HYDRA-IOT/HydraApp/android/app/build/intermediates/cxx/Debug/1n1k36bs/obj/armeabi-v7a/libreact_codegen_safeareacontext.so	96a59754794b33f
5599	13791	7701141874950647	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	42275a47a8a65e18
4659	16527	7701141902313681	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	79cbf29e42743ae1
70	711	7701465216366976	build.ninja	1bbd746630bfa3a1
15636	21120	7701141948756822	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/States.cpp.o	facf147e6e5ee637
65130	71804	7701142455679556	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	216d903cca91a01b
64217	71228	7701142449864499	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	8221932fb0d6e8ab
34	7599	7701362535655399	CMakeFiles/appmodules.dir/OnLoad.cpp.o	3126013251c73b73
7126	16260	7701141899332088	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	cfe2a59ab0225168
60064	66794	7701142405501035	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a94ba4a7694248275b58ea6e1b34e234/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o	3f4e46cc1119e902
39863	48663	7701142224033718	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ad0053095b3309824e38725beb230ec4/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	2934aadb0e99d45f
66794	72493	7701142462770500	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	58545f04d6a16c58
20926	29382	7701142031072977	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o	dc4bb2e6ede4ae5e
42225	51779	7701142255293787	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3d8ed2bade843d889bd16dd10152ddbf/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	603a9064d703c0ed
21035	27678	7701142013474477	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o	d8013737a62d9f76
7391	15636	7701141893823492	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	41289bf6992b5d69
62419	70936	7701142446969994	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a94ba4a7694248275b58ea6e1b34e234/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o	2b2fd3c7ad18e0e
35241	41804	7701142154670618	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/afeb5cd97c1b24e6ae06486f4d02acf9/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o	818b1081c5577be
22249	28237	7701142019711977	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o	f81cca6fb484404
57497	62419	7701142361817642	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a75f7d151350c2ad12e3fcc23620b108/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o	d73cc31da1042579
24311	31467	7701142052282383	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o	d7e64777b95f175f
27678	35240	7701142089739057	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o	ef338f2b4768fb4c
63826	70093	7701142437932975	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	7c1ea1c6b9b59c70
29382	37718	7701142114380734	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9c69b1f7d8ca70e3a7cc148200b22236/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o	15d68295f98c7cde
31068	37601	7701142113075532	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f17cb6db24b6c3165dd89b8b5f73a508/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o	1d3418f969fd7ac6
27828	36991	7701142106933746	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/511fd879f14d465cfd4f7efe96b769f8/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	c289adbcc13bd8be
21425	22449	7701362683615868	E:/HYDRA-IOT/HydraApp/android/app/build/intermediates/cxx/Debug/1n1k36bs/obj/armeabi-v7a/libappmodules.so	da9a04505b833d8d
28473	36067	7701142098298162	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9c69b1f7d8ca70e3a7cc148200b22236/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o	13460a38196bab03
66018	72798	7701142465899396	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	63b6816cbc1acd2c
50921	63273	7701142368129156	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o	a7e729aaa3226bd3
28237	33043	7701142067991766	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9c69b1f7d8ca70e3a7cc148200b22236/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o	761cb5b01e0f725f
33043	39863	7701142135981971	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/25e3f1442121f0e330c9ba177608309a/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o	78021a31238e7ac8
29	21424	7701362672261740	CMakeFiles/appmodules.dir/E_/HYDRA-IOT/HydraApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	d2f34e586344d53b
62878	63826	7701142372931179	E:/HYDRA-IOT/HydraApp/android/app/build/intermediates/cxx/Debug/1n1k36bs/obj/armeabi-v7a/libreact_codegen_rnscreens.so	99c051de5eff9a42
48664	62877	7701142364673002	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/598dc9a4de586092988288a9ed8b2a9c/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	635f9355c113b3f6
36992	45929	7701142196251446	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	46351dd1bf3438f6
44533	52228	7701142259775481	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3d8ed2bade843d889bd16dd10152ddbf/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	e451c41fe386fef5
36067	44533	7701142182863222	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	9614ec882322366d
14325	20926	7701141947064498	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/EventEmitters.cpp.o	437e20f25ffb11b5
63274	71093	7701142448599210	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o	be1a5e1309a6b97b
45929	54534	7701142282789526	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3d8ed2bade843d889bd16dd10152ddbf/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	24f9ab2853e837e4
42913	50332	7701142240282898	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3d8ed2bade843d889bd16dd10152ddbf/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	b74578411db02f
70094	73626	7701142474207692	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	c627ec4e382b4598
47088	55937	7701142296769454	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	7e35cae03d7324d1
55938	65130	7701142389013476	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o	6d8e206e0daa5caf
34814	43653	7701142173983619	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	43e94936b2443981
43653	50921	7701142246800272	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	1e1959de3210c83f
40674	47248	7701142209940294	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/598dc9a4de586092988288a9ed8b2a9c/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	528c4692981371d1
34	7056	7701141808001730	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	c7dd02bab04fada0
54534	66018	7701142397251131	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/e6d3b181a5294a323c839dad0b3bb024/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o	10c73f41d51f12df
37718	47088	7701142207539400	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/50238a92379787fd4f2f91cf6600c217/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	b7742a1ab481f0d4
37602	42224	7701142159842122	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/50238a92379787fd4f2f91cf6600c217/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	f828a7193ca6da99
50332	56318	7701142300674834	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o	354cc48965ce3bac
52228	60064	7701142337649797	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/1c87c49df263958b8dabdd0dcd75ffae/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o	772af3e68762e89a
51779	60528	7701142342669027	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o	ccd7bf7168502122
56319	64216	7701142379692060	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a94ba4a7694248275b58ea6e1b34e234/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o	82d7d0005ad181a4
60529	72035	7701142457629944	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a75f7d151350c2ad12e3fcc23620b108/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o	ec8bd62529b2e7b1
20818	27828	7701142015795083	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/Props.cpp.o	f67df78429a30c92
16528	24311	7701141980735920	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/ShadowNodes.cpp.o	98fccce8b3d16519
13581	20818	7701141945683952	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/RNImagePickerSpec-generated.cpp.o	317dfcf6e8bfc5b8
16261	23591	7701141972800619	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/RNImagePickerSpecJSI-generated.cpp.o	9fbd7ba52fd28f4f
0	142	0	clean	1ffa0f1d946a52ac
32	5599	7701141793501759	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	42093b9b46915256
37	5581	7701141793241740	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	870caefd2f48880e
43	4659	7701141783105474	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	8829dec157612d88
40	7391	7701141811517623	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	234b73d7b00cc071
46	7126	7701141808871823	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	92d9fb0133c7d04
5582	14325	7701141880767261	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	d2ce6f7b77b4c23b
1	34	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/armeabi-v7a/CMakeFiles/cmake.verify_globs	105cfe73fb189e6d
1	264	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/armeabi-v7a/CMakeFiles/cmake.verify_globs	105cfe73fb189e6d
1	34	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/armeabi-v7a/CMakeFiles/cmake.verify_globs	105cfe73fb189e6d
1	37	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/armeabi-v7a/CMakeFiles/cmake.verify_globs	105cfe73fb189e6d
1	35	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/armeabi-v7a/CMakeFiles/cmake.verify_globs	105cfe73fb189e6d

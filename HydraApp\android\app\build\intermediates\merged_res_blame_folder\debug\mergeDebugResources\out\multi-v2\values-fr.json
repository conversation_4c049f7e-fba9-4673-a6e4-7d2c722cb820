{"logs": [{"outputFile": "com.hydraiot.app-mergeDebugResources-51:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79f6ab95733be72bb6af37d95e29f537\\transformed\\browser-1.4.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "66,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "6943,7213,7315,7434", "endColumns": "106,101,118,104", "endOffsets": "7045,7310,7429,7534"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0a1c3fd62b5fb636be47a17086a4f32c\\transformed\\play-services-basement-18.5.0\\res\\values-fr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "160", "endOffsets": "355"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5604", "endColumns": "164", "endOffsets": "5764"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7312d9ce0e64b42e563cee99ce0c7cbc\\transformed\\play-services-base-18.5.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,471,597,702,869,998,1115,1224,1398,1506,1687,1819,1975,2150,2219,2282", "endColumns": "101,175,125,104,166,128,116,108,173,107,180,131,155,174,68,62,79", "endOffsets": "294,470,596,701,868,997,1114,1223,1397,1505,1686,1818,1974,2149,2218,2281,2361"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4541,4647,4827,4957,5066,5237,5370,5491,5769,5947,6059,6244,6380,6540,6719,6792,6859", "endColumns": "105,179,129,108,170,132,120,112,177,111,184,135,159,178,72,66,83", "endOffsets": "4642,4822,4952,5061,5232,5365,5486,5599,5942,6054,6239,6375,6535,6714,6787,6854,6938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3135f157d17fbe3966fe84b667792db9\\transformed\\core-1.13.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "37,38,39,40,41,42,43,138", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3418,3516,3618,3717,3819,3923,4027,12878", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "3511,3613,3712,3814,3918,4022,4140,12974"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa0e231f4d27dc8a031f0119595b6f1\\transformed\\material-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,321,419,549,634,700,797,880,946,1048,1123,1179,1258,1318,1372,1494,1553,1615,1669,1751,1886,1978,2062,2176,2255,2336,2429,2496,2562,2642,2723,2826,2899,2977,3050,3122,3215,3287,3379,3471,3545,3629,3721,3778,3844,3927,4014,4076,4140,4203,4305,4403,4500,4601", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,97,129,84,65,96,82,65,101,74,55,78,59,53,121,58,61,53,81,134,91,83,113,78,80,92,66,65,79,80,102,72,77,72,71,92,71,91,91,73,83,91,56,65,82,86,61,63,62,101,97,96,100,88", "endOffsets": "233,316,414,544,629,695,792,875,941,1043,1118,1174,1253,1313,1367,1489,1548,1610,1664,1746,1881,1973,2057,2171,2250,2331,2424,2491,2557,2637,2718,2821,2894,2972,3045,3117,3210,3282,3374,3466,3540,3624,3716,3773,3839,3922,4009,4071,4135,4198,4300,4398,4495,4596,4685"}, "to": {"startLines": "2,36,44,45,46,67,68,73,75,77,78,79,80,81,82,83,84,85,86,87,88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3335,4145,4243,4373,7050,7116,7609,7775,7908,8010,8085,8141,8220,8280,8334,8456,8515,8577,8631,8713,9009,9101,9185,9299,9378,9459,9552,9619,9685,9765,9846,9949,10022,10100,10173,10245,10338,10410,10502,10594,10668,10752,10844,10901,10967,11050,11137,11199,11263,11326,11428,11526,11623,11724", "endLines": "5,36,44,45,46,67,68,73,75,77,78,79,80,81,82,83,84,85,86,87,88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "endColumns": "12,82,97,129,84,65,96,82,65,101,74,55,78,59,53,121,58,61,53,81,134,91,83,113,78,80,92,66,65,79,80,102,72,77,72,71,92,71,91,91,73,83,91,56,65,82,86,61,63,62,101,97,96,100,88", "endOffsets": "283,3413,4238,4368,4453,7111,7208,7687,7836,8005,8080,8136,8215,8275,8329,8451,8510,8572,8626,8708,8843,9096,9180,9294,9373,9454,9547,9614,9680,9760,9841,9944,10017,10095,10168,10240,10333,10405,10497,10589,10663,10747,10839,10896,10962,11045,11132,11194,11258,11321,11423,11521,11618,11719,11808"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd1664c1c7ce01ff711fc15460fc5485\\transformed\\credentials-1.2.0-rc01\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,171", "endColumns": "115,118", "endOffsets": "166,285"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3100,3216", "endColumns": "115,118", "endOffsets": "3211,3330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af63e50a53e992ea9c751fc2deb2fc57\\transformed\\appcompat-1.7.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "288,399,514,624,706,812,942,1020,1096,1187,1280,1378,1473,1573,1666,1759,1854,1945,2036,2122,2232,2343,2446,2557,2665,2772,2931,12153", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "394,509,619,701,807,937,1015,1091,1182,1275,1373,1468,1568,1661,1754,1849,1940,2031,2117,2227,2338,2441,2552,2660,2767,2926,3025,12235"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ff5b0ce559ae890269420b3dec0e4060\\transformed\\react-android-0.79.2-debug\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,208,278,361,428,507,589,679,771,842,929,1004,1091,1171,1251,1326,1403,1476,1567,1646,1727,1799", "endColumns": "69,82,69,82,66,78,81,89,91,70,86,74,86,79,79,74,76,72,90,78,80,71,79", "endOffsets": "120,203,273,356,423,502,584,674,766,837,924,999,1086,1166,1246,1321,1398,1471,1562,1641,1722,1794,1874"}, "to": {"startLines": "33,47,72,74,76,89,90,125,126,127,128,130,131,132,133,134,135,136,137,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3030,4458,7539,7692,7841,8848,8927,11813,11903,11995,12066,12240,12315,12402,12482,12562,12637,12714,12787,12979,13058,13139,13211", "endColumns": "69,82,69,82,66,78,81,89,91,70,86,74,86,79,79,74,76,72,90,78,80,71,79", "endOffsets": "3095,4536,7604,7770,7903,8922,9004,11898,11990,12061,12148,12310,12397,12477,12557,12632,12709,12782,12873,13053,13134,13206,13286"}}]}]}
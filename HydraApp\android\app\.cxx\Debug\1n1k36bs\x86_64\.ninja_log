# ninja log v5
1	27	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/x86_64/CMakeFiles/cmake.verify_globs	8c196a5011669cbc
5688	11758	7701143443825351	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	a104048a24b2c1db
6465	11392	7701143440381252	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o	a3e98c62c51208d9
7420	14721	7701143472736106	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	72e1241f79761bb0
60305	66299	7701143988277265	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	754d1841869a4d91
16780	25970	7701143585787405	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o	60a8d5981cdb8a1e
38670	44721	7701143773533677	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1c87c49df263958b8dabdd0dcd75ffae/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	584d5e27f3e2d16c
37842	45670	7701143783035849	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3d8ed2bade843d889bd16dd10152ddbf/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	d226de550bebcc0e
19536	24987	7701143576319057	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o	6cb535d7c1ef5a23
5626	16779	7701143493746464	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	b2f34d7b366f1427
77	633	7701465311570295	build.ninja	4080f42dece6cc2d
62698	69180	7701144018487400	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	3528e89e1135adb6
62044	68181	7701144008185659	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	533e6865268bc1d5
23	7557	7701363402185783	CMakeFiles/appmodules.dir/OnLoad.cpp.o	27ee587ae2faf039
11393	19031	7701143516755448	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o	4d677cb05e2e25c
44119	51741	7701143843167614	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/50238a92379787fd4f2f91cf6600c217/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	683e390f2b21872a
31120	37003	7701143696519300	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/550838433058dc1fd573b81ffaad7ac7/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o	c1d27163ba388c1
7113	16918	7701143495323227	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	7ea81b1583b90ab1
24988	32138	7701143647822453	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o	4464a00004e2e983
42188	44067	7701143763130231	E:/HYDRA-IOT/HydraApp/android/app/build/intermediates/cxx/Debug/1n1k36bs/obj/x86_64/libreact_codegen_safeareacontext.so	96dd145525ee67c7
7739	14279	7701143468506168	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	693ca684159f9165
50554	59368	7701143920001421	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a75f7d151350c2ad12e3fcc23620b108/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o	6a51997c83222425
23176	28729	7701143613466028	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o	97947196e5291dc8
22509	29788	7701143624499726	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o	a99c6c46e8f2540f
50564	55734	7701143883743253	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a75f7d151350c2ad12e3fcc23620b108/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o	11b9d13c6100d232
21921	29112	7701143617535172	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o	2f155579a863ef2b
24723	31120	7701143637588625	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o	43b54c44d1564d3f
29112	35124	7701143677248489	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9c69b1f7d8ca70e3a7cc148200b22236/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o	931e06a8f1ad35f7
59703	66461	7701143991005657	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	ea59dedfe450d259
26910	37131	7701143697484660	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b7046d699b6e0fe79e4beb14c8a51d24/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o	f4fd65c6f0d4385f
26	20247	7701363524915832	CMakeFiles/appmodules.dir/E_/HYDRA-IOT/HydraApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	7d28b58f619fb7db
29789	36794	7701143692884941	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/891b7855b2d125d04ac7afa152cad07e/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o	38777c03f5b05335
37132	43470	7701143760762964	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	f307794b965d3765
25971	35306	7701143678976488	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f17cb6db24b6c3165dd89b8b5f73a508/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	ae4ff8442c71e0a0
44356	50564	7701143831934050	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/598dc9a4de586092988288a9ed8b2a9c/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	eb07d797ea3c0acf
35307	42187	7701143748304247	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/847453d9e4667eb7e9460afd25ed1195/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o	e8c756dbafe812b1
27699	35718	7701143683498091	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9c69b1f7d8ca70e3a7cc148200b22236/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o	7a8cd3228d5ca9da
28729	37841	7701143704713761	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/550838433058dc1fd573b81ffaad7ac7/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o	816fd96ae349a7f
12378	19521	7701143521437076	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/EventEmitters.cpp.o	5a30680b2d0bedf8
65986	70890	7701144035645434	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o	44569884eea7e03f
51742	62698	7701143952908675	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a94ba4a7694248275b58ea6e1b34e234/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o	b79c13bfa0a02559
43168	51602	7701143842382367	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3d8ed2bade843d889bd16dd10152ddbf/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	cf44a901b83363e
32138	38669	7701143712987949	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8311d7994bdb89babd489a4af328892e/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o	6dc1f9139f08707f
57923	59196	7701143914654913	E:/HYDRA-IOT/HydraApp/android/app/build/intermediates/cxx/Debug/1n1k36bs/obj/x86_64/libreact_codegen_rnscreens.so	d5e52b554971588b
43470	50037	7701143826830193	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	5949e872f7065da2
66299	69883	7701144025597920	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	b3fd9804ffe1b1f5
37004	45588	7701143782010612	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	c13a659f2cca407b
59063	67928	7701144005689881	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o	3fa389b536c1c51a
35719	44355	7701143769946269	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	6f99e457fd1b152f
35124	43168	7701143758072331	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	b7bbb2fc7d48c824
45963	57923	7701143904849642	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/598dc9a4de586092988288a9ed8b2a9c/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	be58236322ea007d
36795	45963	7701143786165334	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2ed5f7f3a92f765aa203564f78e946ce/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	fbd1c93c41717402
45589	50554	7701143831693968	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/50238a92379787fd4f2f91cf6600c217/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	3c7f6ab6b5a865cc
44721	53718	7701143862075050	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/50238a92379787fd4f2f91cf6600c217/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	de8d5faaa332073e
45670	55191	7701143878172883	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ad0053095b3309824e38725beb230ec4/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	8ca96ea6f339b6fa
53719	59703	7701143922857027	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o	6dfecf8f80055d08
64762	70331	7701144030085117	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	df70c9bb5ba6b911
59196	68184	7701144008225726	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o	7baa1ec8ac6a9e2c
55191	62044	7701143946903507	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o	2b63862cae6c5ffd
59369	65985	7701143986335545	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a75f7d151350c2ad12e3fcc23620b108/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o	6b9de10e170de7fe
20248	25003	7701363576020945	E:/HYDRA-IOT/HydraApp/android/app/build/intermediates/cxx/Debug/1n1k36bs/obj/x86_64/libappmodules.so	8ea39fd6615f272c
55734	64762	7701143973236906	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/E_/HYDRA-IOT/HydraApp/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o	a5396c776c12b529
51602	59063	7701143916785456	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a94ba4a7694248275b58ea6e1b34e234/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o	eb6340551858662d
50038	60304	7701143929267929	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a75f7d151350c2ad12e3fcc23620b108/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o	db53bb33e51c8722
68185	68816	7701144014019573	E:/HYDRA-IOT/HydraApp/android/app/build/intermediates/cxx/Debug/1n1k36bs/obj/x86_64/libreact_codegen_rnsvg.so	4c0e6a155cbbadf8
19521	26910	7701143595130013	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/Props.cpp.o	7cc93e372356d60e
16919	23176	7701143557942036	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/States.cpp.o	a8a0ce05f2ad25c9
11758	19536	7701143521775340	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/ComponentDescriptors.cpp.o	f0f716e31fdf7d41
19031	27698	7701143602758471	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/ShadowNodes.cpp.o	a0c40aa357353db3
14279	21921	7701143545569455	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/RNImagePickerSpec-generated.cpp.o	40fd87e5498cd064
14722	22509	7701143551306548	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/RNImagePickerSpecJSI-generated.cpp.o	e7c403b49d70d470
0	21	0	clean	1ffa0f1d946a52ac
43	5688	7701143383017843	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	f0b8db512f233a8
54	5626	7701143382602673	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	f937c58bf96a919a
145	4898	7701143374282469	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	6f43650133a7bee5
51	6464	7701143391047925	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	7909697d8d45f373
48	7112	7701143397470128	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	d0eac7782c3f265f
142	7419	7701143400218215	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	76ac40c571f748ed
4899	12378	7701143450274668	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	d051d0e41d2ef1d6
1	35	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/x86_64/CMakeFiles/cmake.verify_globs	8c196a5011669cbc
4	43	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/x86_64/CMakeFiles/cmake.verify_globs	8c196a5011669cbc
1	33	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/x86_64/CMakeFiles/cmake.verify_globs	8c196a5011669cbc
0	33	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/x86_64/CMakeFiles/cmake.verify_globs	8c196a5011669cbc
1	35	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/x86_64/CMakeFiles/cmake.verify_globs	8c196a5011669cbc
1	65	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/x86_64/CMakeFiles/cmake.verify_globs	8c196a5011669cbc
1	32	0	E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/x86_64/CMakeFiles/cmake.verify_globs	8c196a5011669cbc
0	37	0	clean	1ffa0f1d946a52ac

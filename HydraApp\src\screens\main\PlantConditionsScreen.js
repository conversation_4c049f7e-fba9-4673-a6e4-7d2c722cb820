import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import { COLORS, SIZES, FONTS, SHADOWS } from '../../constants/theme';
import Button from '../../components/Button';

const PlantConditionsScreen = ({ route, navigation }) => {
  const { plantData, onSave } = route.params;
  const [loading, setLoading] = useState(false);
  
  const [conditions, setConditions] = useState(
    plantData.conditions || {
      soilMoisture: { min: 30, max: 70 },
      temperature: { min: 18, max: 28 },
      humidity: { min: 40, max: 80 },
    }
  );

  const handleConditionChange = (condition, type, value) => {
    const numValue = parseInt(value) || 0;
    setConditions(prev => ({
      ...prev,
      [condition]: {
        ...prev[condition],
        [type]: numValue,
      },
    }));
  };

  const validateConditions = () => {
    const errors = [];

    Object.keys(conditions).forEach(condition => {
      const { min, max } = conditions[condition];
      if (min >= max) {
        errors.push(`${condition}: Minimum value must be less than maximum value`);
      }
      if (min < 0 || max < 0) {
        errors.push(`${condition}: Values cannot be negative`);
      }
    });

    if (errors.length > 0) {
      Alert.alert('Validation Error', errors.join('\n'));
      return false;
    }
    return true;
  };

  const handleSave = async () => {
    if (!validateConditions()) return;

    setLoading(true);
    try {
      const updatedPlantData = {
        ...plantData,
        conditions,
      };
      await onSave(updatedPlantData);
      navigation.goBack();
    } catch (error) {
      console.error('Error saving conditions:', error);
      Alert.alert('Error', 'Failed to save conditions');
    } finally {
      setLoading(false);
    }
  };

  const getConditionIcon = (condition) => {
    switch (condition) {
      case 'soilMoisture':
        return 'water-percent';
      case 'temperature':
        return 'thermometer';
      case 'humidity':
        return 'water';
      default:
        return 'cog';
    }
  };

  const getConditionLabel = (condition) => {
    switch (condition) {
      case 'soilMoisture':
        return 'Soil Moisture';
      case 'temperature':
        return 'Temperature';
      case 'humidity':
        return 'Humidity';
      default:
        return condition;
    }
  };

  const getConditionUnit = (condition) => {
    switch (condition) {
      case 'soilMoisture':
        return '%';
      case 'temperature':
        return '°C';
      case 'humidity':
        return '%';
      default:
        return '';
    }
  };

  const getConditionColor = (condition) => {
    switch (condition) {
      case 'soilMoisture':
        return COLORS.primary;
      case 'temperature':
        return COLORS.secondary;
      case 'humidity':
        return COLORS.accent;
      default:
        return COLORS.gray600;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />

      <LinearGradient
        colors={COLORS.gradientPrimary}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.headerGradient}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-left" size={SIZES.iconLarge} color={COLORS.white} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Plant Conditions</Text>
          <View style={styles.headerSpacer} />
        </View>

        <View style={styles.headerContent}>
          <Text style={styles.headerSubtitle}>
            Set the ideal conditions for {plantData.name}
          </Text>
          <Text style={styles.headerDescription}>
            These ranges will determine when your plant needs attention
          </Text>
        </View>
      </LinearGradient>

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.infoCard}>
          <Icon name="information" size={SIZES.iconMedium} color={COLORS.info} />
          <Text style={styles.infoText}>
            Set minimum and maximum values for each condition. The system will alert you when values go outside these ranges.
          </Text>
        </View>

        {Object.keys(conditions).map((condition) => (
          <View key={condition} style={styles.conditionCard}>
            <View style={styles.conditionHeader}>
              <View style={[
                styles.conditionIconContainer,
                { backgroundColor: getConditionColor(condition) + '20' }
              ]}>
                <Icon
                  name={getConditionIcon(condition)}
                  size={SIZES.iconMedium}
                  color={getConditionColor(condition)}
                />
              </View>
              <View style={styles.conditionInfo}>
                <Text style={styles.conditionTitle}>
                  {getConditionLabel(condition)}
                </Text>
                <Text style={styles.conditionSubtitle}>
                  Current range: {conditions[condition].min} - {conditions[condition].max}{getConditionUnit(condition)}
                </Text>
              </View>
            </View>

            <View style={styles.rangeInputs}>
              <View style={styles.rangeInput}>
                <Text style={styles.rangeLabel}>Minimum {getConditionUnit(condition)}</Text>
                <TextInput
                  style={styles.rangeTextInput}
                  value={conditions[condition].min.toString()}
                  onChangeText={(value) => handleConditionChange(condition, 'min', value)}
                  keyboardType="numeric"
                  placeholder="0"
                />
              </View>

              <View style={styles.rangeSeparator}>
                <Text style={styles.rangeSeparatorText}>to</Text>
              </View>

              <View style={styles.rangeInput}>
                <Text style={styles.rangeLabel}>Maximum {getConditionUnit(condition)}</Text>
                <TextInput
                  style={styles.rangeTextInput}
                  value={conditions[condition].max.toString()}
                  onChangeText={(value) => handleConditionChange(condition, 'max', value)}
                  keyboardType="numeric"
                  placeholder="100"
                />
              </View>
            </View>

            <View style={styles.conditionPresets}>
              <Text style={styles.presetsLabel}>Quick presets:</Text>
              <View style={styles.presetButtons}>
                {getPresets(condition).map((preset, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.presetButton}
                    onPress={() => {
                      setConditions(prev => ({
                        ...prev,
                        [condition]: preset.range,
                      }));
                    }}
                  >
                    <Text style={styles.presetButtonText}>{preset.label}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>
        ))}

        <Button
          title="Save Conditions"
          onPress={handleSave}
          loading={loading}
          style={styles.saveButton}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

const getPresets = (condition) => {
  switch (condition) {
    case 'soilMoisture':
      return [
        { label: 'Dry Plants', range: { min: 20, max: 40 } },
        { label: 'Normal', range: { min: 30, max: 70 } },
        { label: 'Moist Plants', range: { min: 50, max: 80 } },
      ];
    case 'temperature':
      return [
        { label: 'Cool', range: { min: 15, max: 22 } },
        { label: 'Normal', range: { min: 18, max: 28 } },
        { label: 'Warm', range: { min: 25, max: 35 } },
      ];
    case 'humidity':
      return [
        { label: 'Low', range: { min: 30, max: 50 } },
        { label: 'Normal', range: { min: 40, max: 80 } },
        { label: 'High', range: { min: 60, max: 90 } },
      ];
    default:
      return [];
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  headerGradient: {
    paddingBottom: SIZES.paddingLarge,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SIZES.paddingLarge,
    paddingTop: SIZES.paddingLarge,
    paddingBottom: SIZES.paddingMedium,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: FONTS.h3,
    fontWeight: '700',
    color: COLORS.white,
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40,
  },
  headerContent: {
    paddingHorizontal: SIZES.paddingLarge,
  },
  headerSubtitle: {
    fontSize: FONTS.h4,
    fontWeight: '600',
    color: COLORS.white,
    marginBottom: SIZES.marginXS,
  },
  headerDescription: {
    fontSize: FONTS.body3,
    color: COLORS.white,
    opacity: 0.9,
  },
  scrollContent: {
    paddingHorizontal: SIZES.paddingLarge,
    paddingBottom: SIZES.paddingLarge,
  },
  infoCard: {
    flexDirection: 'row',
    backgroundColor: COLORS.infoLight + '20',
    borderRadius: SIZES.radiusMedium,
    padding: SIZES.paddingMedium,
    marginBottom: SIZES.marginLarge,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.info,
  },
  infoText: {
    flex: 1,
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    marginLeft: SIZES.marginMedium,
    lineHeight: 20,
  },
  conditionCard: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radiusLarge,
    padding: SIZES.paddingLarge,
    marginBottom: SIZES.marginMedium,
    ...SHADOWS.medium,
  },
  conditionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SIZES.marginMedium,
  },
  conditionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SIZES.marginMedium,
  },
  conditionInfo: {
    flex: 1,
  },
  conditionTitle: {
    fontSize: FONTS.h4,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: 2,
  },
  conditionSubtitle: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
  },
  rangeInputs: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SIZES.marginMedium,
  },
  rangeInput: {
    flex: 1,
  },
  rangeLabel: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    marginBottom: SIZES.marginXS,
  },
  rangeTextInput: {
    borderWidth: 1,
    borderColor: COLORS.gray300,
    borderRadius: SIZES.radiusMedium,
    paddingHorizontal: SIZES.paddingMedium,
    paddingVertical: SIZES.paddingSmall,
    fontSize: FONTS.body2,
    color: COLORS.textPrimary,
    textAlign: 'center',
  },
  rangeSeparator: {
    paddingHorizontal: SIZES.paddingMedium,
    paddingTop: SIZES.paddingMedium,
  },
  rangeSeparatorText: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
  },
  conditionPresets: {
    borderTopWidth: 1,
    borderTopColor: COLORS.gray200,
    paddingTop: SIZES.paddingMedium,
  },
  presetsLabel: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    marginBottom: SIZES.marginSmall,
  },
  presetButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SIZES.marginSmall,
  },
  presetButton: {
    backgroundColor: COLORS.gray100,
    borderRadius: SIZES.radiusSmall,
    paddingHorizontal: SIZES.paddingSmall,
    paddingVertical: SIZES.paddingXS,
  },
  presetButtonText: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
  },
  saveButton: {
    marginTop: SIZES.marginLarge,
  },
});

export default PlantConditionsScreen;

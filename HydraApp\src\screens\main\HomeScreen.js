import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Image,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import { COLORS, SIZES, FONTS, SHADOWS } from '../../constants/theme';
import { useAuth } from '../../context/AuthContext';
import PlantCard from '../../components/PlantCard';
import PlantImage from '../../components/PlantImage';
import { PLANTS } from '../../constants/plants';
import { subscribePlantSensorData } from '../../services/databaseService';

const HomeScreen = ({ navigation }) => {
  const { userProfile } = useAuth();
  const [plants, setPlants] = useState(PLANTS);
  const [sensorData, setSensorData] = useState({});
  const [refreshing, setRefreshing] = useState(false);

  // Load sensor data for each plant
  useEffect(() => {
    const unsubscribes = plants.map(plant => {
      return subscribePlantSensorData(plant.id, (data) => {
        setSensorData(prev => ({
          ...prev,
          [plant.id]: data
        }));
      });
    });

    // Cleanup subscriptions on unmount
    return () => {
      unsubscribes.forEach(unsubscribe => unsubscribe());
    };
  }, [plants]);

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);

    // Simulate refresh delay
    setTimeout(() => {
      setRefreshing(false);
    }, 1500);
  };

  // Navigate to plant detail screen
  const handlePlantPress = (plant) => {
    navigation.navigate('PlantDetail', {
      plant,
      sensorData: sensorData[plant.id] || {
        soilMoisture: 0,
        temperature: 0,
        humidity: 0
      }
    });
  };

  // Get greeting based on time of day
  const getGreeting = () => {
    const hour = new Date().getHours();

    if (hour < 12) {
      return 'Good Morning';
    } else if (hour < 18) {
      return 'Good Afternoon';
    } else {
      return 'Good Evening';
    }
  };

  // Count plants that need attention
  const getPlantsNeedingAttention = () => {
    return plants.filter(plant => {
      const data = sensorData[plant.id];
      if (!data) return false;

      const { soilMoisture, temperature, humidity } = data;
      const { optimalConditions } = plant;

      return (
        soilMoisture < optimalConditions.soilMoisture.min ||
        soilMoisture > optimalConditions.soilMoisture.max ||
        temperature < optimalConditions.temperature.min ||
        temperature > optimalConditions.temperature.max ||
        humidity < optimalConditions.humidity.min ||
        humidity > optimalConditions.humidity.max
      );
    }).length;
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />

      <LinearGradient
        colors={COLORS.gradientPrimary}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.headerGradient}
      >
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <View style={styles.greetingContainer}>
              <Text style={styles.greeting}>{getGreeting()}</Text>
              <Text style={styles.username}>{userProfile?.displayName || 'User'}</Text>
            </View>

            <TouchableOpacity
              style={styles.profileButton}
              onPress={() => navigation.navigate('Profile')}
            >
              <PlantImage
                plantId="user-profile-header"
                imageUrl={userProfile?.photoURL}
                plantType="user"
                size="small"
                editable={false}
                style={styles.profileImageContainer}
              />
            </TouchableOpacity>
          </View>

          {/* Quick stats in header */}
          <View style={styles.quickStats}>
            <View style={styles.statItem}>
              <Icon name="leaf" size={SIZES.iconMedium} color={COLORS.white} />
              <Text style={styles.statValue}>{plants.length}</Text>
              <Text style={styles.statLabel}>Plants</Text>
            </View>
            <View style={styles.statItem}>
              <Icon name="alert-circle" size={SIZES.iconMedium} color={COLORS.white} />
              <Text style={styles.statValue}>{getPlantsNeedingAttention()}</Text>
              <Text style={styles.statLabel}>Alerts</Text>
            </View>
            <View style={styles.statItem}>
              <Icon name="water-pump" size={SIZES.iconMedium} color={COLORS.white} />
              <Text style={styles.statValue}>0</Text>
              <Text style={styles.statLabel}>Active</Text>
            </View>
          </View>
        </View>
      </LinearGradient>

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[COLORS.primary]}
            tintColor={COLORS.primary}
          />
        }
      >
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Your Plants</Text>
          <TouchableOpacity style={styles.addButton}>
            <Icon name="plus" size={SIZES.iconMedium} color={COLORS.primary} />
          </TouchableOpacity>
        </View>

        {plants.map(plant => (
          <PlantCard
            key={plant.id}
            plant={plant}
            sensorData={sensorData[plant.id] || {
              soilMoisture: 0,
              temperature: 0,
              humidity: 0
            }}
            onPress={() => handlePlantPress(plant)}
          />
        ))}

        {plants.length === 0 && (
          <View style={styles.emptyState}>
            <Icon name="leaf-off" size={SIZES.iconXXL} color={COLORS.gray400} />
            <Text style={styles.emptyStateTitle}>No Plants Yet</Text>
            <Text style={styles.emptyStateText}>
              Add your first plant to start monitoring
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  headerGradient: {
    paddingBottom: SIZES.paddingLarge,
  },
  header: {
    paddingHorizontal: SIZES.paddingLarge,
    paddingTop: SIZES.paddingLarge,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SIZES.marginLarge,
  },
  greetingContainer: {
    flex: 1,
  },
  greeting: {
    fontSize: FONTS.h5,
    color: COLORS.white,
    opacity: 0.9,
    marginBottom: SIZES.marginXS,
  },
  username: {
    fontSize: FONTS.h2,
    fontWeight: '700',
    color: COLORS.white,
  },
  profileButton: {
    padding: SIZES.paddingXS,
  },
  profileImageContainer: {
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 30, // Half of the small size (60px) for perfect circle
    overflow: 'hidden',
  },
  quickStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: SIZES.radiusLarge,
    paddingVertical: SIZES.paddingMedium,
    paddingHorizontal: SIZES.paddingSmall,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: FONTS.h3,
    fontWeight: '700',
    color: COLORS.white,
    marginTop: SIZES.marginXS,
  },
  statLabel: {
    fontSize: FONTS.body3,
    color: COLORS.white,
    opacity: 0.8,
    marginTop: 2,
  },
  scrollContent: {
    paddingHorizontal: SIZES.paddingLarge,
    paddingTop: SIZES.paddingMedium,
    paddingBottom: SIZES.paddingLarge,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.marginMedium,
  },
  sectionTitle: {
    fontSize: FONTS.h3,
    fontWeight: '700',
    color: COLORS.textPrimary,
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: COLORS.primaryLightest,
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.small,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SIZES.paddingXL * 2,
  },
  emptyStateTitle: {
    fontSize: FONTS.h3,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginTop: SIZES.marginMedium,
    marginBottom: SIZES.marginSmall,
  },
  emptyStateText: {
    fontSize: FONTS.body2,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
});

export default HomeScreen;

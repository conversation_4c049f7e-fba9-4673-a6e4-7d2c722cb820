import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import { COLORS, SIZES, FONTS, SHADOWS } from '../../constants/theme';
import { useAuth } from '../../context/AuthContext';
import { getUserPlants, subscribePlantSensorData } from '../../services/databaseService';
import ExpandablePlantCard from '../../components/ExpandablePlantCard';

const PlantDetailsScreen = ({ navigation }) => {
  const { currentUser } = useAuth();
  const [plants, setPlants] = useState([]);
  const [sensorData, setSensorData] = useState({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [expandedCards, setExpandedCards] = useState(new Set());

  // Load user plants on component mount
  useEffect(() => {
    loadUserPlants();
  }, [currentUser]);

  // Subscribe to sensor data for all plants
  useEffect(() => {
    if (plants.length === 0) return;

    const unsubscribes = plants.map(plant => {
      if (plant.potId) {
        return subscribePlantSensorData(plant.potId, (data) => {
          setSensorData(prev => ({
            ...prev,
            [plant.id]: data
          }));
        });
      }
      return null;
    }).filter(Boolean);

    // Cleanup subscriptions on unmount
    return () => {
      unsubscribes.forEach(unsubscribe => unsubscribe());
    };
  }, [plants]);

  const loadUserPlants = async () => {
    if (!currentUser) return;

    try {
      setLoading(true);
      const userPlants = await getUserPlants(currentUser.uid);
      setPlants(userPlants);
    } catch (error) {
      console.error('Error loading user plants:', error);
      setPlants([]);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadUserPlants();
    } catch (error) {
      console.error('Error refreshing plants:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleCardExpand = (plantId) => {
    setExpandedCards(prev => {
      const newSet = new Set(prev);
      if (newSet.has(plantId)) {
        newSet.delete(plantId);
      } else {
        newSet.add(plantId);
      }
      return newSet;
    });
  };

  const handleEditPlant = (plant) => {
    navigation.navigate('EditPlant', { plant });
  };

  const handleWateringChange = () => {
    // Refresh sensor data after watering actions
    console.log('Watering action completed, refreshing data...');
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />
        <LinearGradient
          colors={COLORS.gradientPrimary}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <Icon name="arrow-left" size={SIZES.iconLarge} color={COLORS.white} />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Plant Details</Text>
            <View style={styles.headerSpacer} />
          </View>
        </LinearGradient>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading plants...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />

      <LinearGradient
        colors={COLORS.gradientPrimary}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.headerGradient}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-left" size={SIZES.iconLarge} color={COLORS.white} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Plant Details</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => navigation.navigate('AddPlant')}
          >
            <Icon name="plus" size={SIZES.iconLarge} color={COLORS.white} />
          </TouchableOpacity>
        </View>

        <View style={styles.headerContent}>
          <Text style={styles.headerSubtitle}>
            {plants.length} {plants.length === 1 ? 'Plant' : 'Plants'}
          </Text>
          <Text style={styles.headerDescription}>
            Tap any plant to view watering controls and detailed information
          </Text>
        </View>
      </LinearGradient>

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[COLORS.primary]}
            tintColor={COLORS.primary}
          />
        }
      >
        {plants.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Icon name="sprout" size={64} color={COLORS.gray400} />
            <Text style={styles.emptyTitle}>No Plants Yet</Text>
            <Text style={styles.emptyDescription}>
              Add your first plant to start monitoring and controlling watering
            </Text>
            <TouchableOpacity
              style={styles.addFirstPlantButton}
              onPress={() => navigation.navigate('AddPlant')}
            >
              <Icon name="plus" size={SIZES.iconMedium} color={COLORS.white} />
              <Text style={styles.addFirstPlantText}>Add Your First Plant</Text>
            </TouchableOpacity>
          </View>
        ) : (
          plants.map((plant) => (
            <ExpandablePlantCard
              key={plant.id}
              plant={plant}
              sensorData={sensorData[plant.id] || {
                soilMoisture: 0,
                temperature: 0,
                humidity: 0,
              }}
              isExpanded={expandedCards.has(plant.id)}
              onExpand={() => handleCardExpand(plant.id)}
              onEdit={() => handleEditPlant(plant)}
              onWateringChange={handleWateringChange}
            />
          ))
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  headerGradient: {
    paddingBottom: SIZES.paddingLarge,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SIZES.paddingLarge,
    paddingTop: SIZES.paddingLarge,
    paddingBottom: SIZES.paddingMedium,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: FONTS.h3,
    fontWeight: '700',
    color: COLORS.white,
    flex: 1,
    textAlign: 'center',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerSpacer: {
    width: 40,
  },
  headerContent: {
    paddingHorizontal: SIZES.paddingLarge,
  },
  headerSubtitle: {
    fontSize: FONTS.h4,
    fontWeight: '600',
    color: COLORS.white,
    marginBottom: SIZES.marginXS,
  },
  headerDescription: {
    fontSize: FONTS.body3,
    color: COLORS.white,
    opacity: 0.9,
  },
  scrollContent: {
    paddingHorizontal: SIZES.paddingLarge,
    paddingTop: SIZES.paddingLarge,
    paddingBottom: SIZES.paddingXL,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FONTS.body1,
    color: COLORS.textSecondary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SIZES.paddingXL,
  },
  emptyTitle: {
    fontSize: FONTS.h3,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginTop: SIZES.marginLarge,
    marginBottom: SIZES.marginSmall,
  },
  emptyDescription: {
    fontSize: FONTS.body2,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: SIZES.marginLarge,
    paddingHorizontal: SIZES.paddingLarge,
  },
  addFirstPlantButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    paddingHorizontal: SIZES.paddingLarge,
    paddingVertical: SIZES.paddingMedium,
    borderRadius: SIZES.radiusLarge,
    ...SHADOWS.medium,
  },
  addFirstPlantText: {
    fontSize: FONTS.body1,
    fontWeight: '600',
    color: COLORS.white,
    marginLeft: SIZES.marginSmall,
  },
});

export default PlantDetailsScreen;

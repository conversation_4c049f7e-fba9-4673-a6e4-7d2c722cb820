# C/C++ build system timings
generate_cxx_metadata
  [gap of 59ms]
  create-invalidation-state 59ms
  generate-prefab-packages
    exec-prefab 3519ms
    [gap of 83ms]
  generate-prefab-packages completed in 3611ms
  execute-generate-process
    exec-configure 1027ms
    [gap of 196ms]
  execute-generate-process completed in 1224ms
  [gap of 124ms]
  remove-unexpected-so-files 10ms
  [gap of 46ms]
generate_cxx_metadata completed in 5135ms


import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Switch,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, SIZES, FONTS, SHADOWS } from '../constants/theme';
import {
  triggerManualWatering,
  stopWatering,
  setAutomaticWatering,
  setIntervalWatering,
  updateWateringInterval,
  checkIntervalWateringDue,
  getWateringStatus,
  addWateringHistory
} from '../services/databaseService';
import { useAuth } from '../context/AuthContext';

const WateringControls = ({ plant, sensorData, onWateringChange }) => {
  const { currentUser } = useAuth();
  const [isWatering, setIsWatering] = useState(false);
  const [isManualWatering, setIsManualWatering] = useState(false);
  const [automaticEnabled, setAutomaticEnabled] = useState(plant.automaticWatering || false);
  const [intervalEnabled, setIntervalEnabled] = useState(plant.intervalWatering || false);
  const [wateringInterval, setWateringInterval] = useState(plant.wateringInterval || 24);
  const [showIntervalEdit, setShowIntervalEdit] = useState(false);
  const [loading, setLoading] = useState(false);
  const [wateringTimer, setWateringTimer] = useState(null);

  useEffect(() => {
    if (plant.potId) {
      checkWateringStatus();
    }
  }, [plant.potId]);

  useEffect(() => {
    // Cleanup timer on unmount
    return () => {
      if (wateringTimer) {
        clearTimeout(wateringTimer);
      }
    };
  }, [wateringTimer]);

  const checkWateringStatus = async () => {
    try {
      const status = await getWateringStatus(plant.potId);
      setIsWatering(status.isWatering);
      setIsManualWatering(status.isManual);
    } catch (error) {
      console.error('Error checking watering status:', error);
    }
  };

  const handleManualWatering = async (duration = 5) => {
    if (isWatering) {
      // Stop watering
      try {
        setLoading(true);
        await stopWatering(plant.potId);

        // Clear timer
        if (wateringTimer) {
          clearTimeout(wateringTimer);
          setWateringTimer(null);
        }

        // Add to history
        await addWateringHistory(currentUser.uid, plant.id, plant.potId, {
          type: 'manual',
          duration: 'stopped',
          soilMoistureBefore: sensorData?.soilMoisture || 0,
        });

        setIsWatering(false);
        setIsManualWatering(false);

        if (onWateringChange) onWateringChange();

        Alert.alert('Success', 'Watering stopped successfully');
      } catch (error) {
        console.error('Error stopping watering:', error);
        Alert.alert('Error', 'Failed to stop watering');
      } finally {
        setLoading(false);
      }
    } else {
      // Start watering
      Alert.alert(
        'Manual Watering',
        'How long would you like to water your plant?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: '3 seconds', onPress: () => startWatering(3) },
          { text: '5 seconds', onPress: () => startWatering(5) },
          { text: '10 seconds', onPress: () => startWatering(10) },
        ]
      );
    }
  };

  const startWatering = async (duration) => {
    try {
      setLoading(true);
      await triggerManualWatering(plant.potId, duration);

      setIsWatering(true);
      setIsManualWatering(true);

      // Set timer to automatically stop watering
      const timer = setTimeout(async () => {
        try {
          await stopWatering(plant.potId);
          setIsWatering(false);
          setIsManualWatering(false);

          // Add to history
          await addWateringHistory(currentUser.uid, plant.id, plant.potId, {
            type: 'manual',
            duration: duration,
            soilMoistureBefore: sensorData?.soilMoisture || 0,
          });

          if (onWateringChange) onWateringChange();

          Alert.alert('Complete', `Watering completed (${duration}s)`);
        } catch (error) {
          console.error('Error auto-stopping watering:', error);
        }
        setWateringTimer(null);
      }, duration * 1000);

      setWateringTimer(timer);

      if (onWateringChange) onWateringChange();

      Alert.alert('Started', `Watering for ${duration} seconds...`);
    } catch (error) {
      console.error('Error starting watering:', error);
      Alert.alert('Error', 'Failed to start watering');
    } finally {
      setLoading(false);
    }
  };

  const handleAutomaticToggle = async (enabled) => {
    try {
      setLoading(true);
      await setAutomaticWatering(currentUser.uid, plant.id, enabled);
      setAutomaticEnabled(enabled);

      Alert.alert(
        'Success',
        `Condition-based watering ${enabled ? 'enabled' : 'disabled'} for ${plant.name}`
      );
    } catch (error) {
      console.error('Error toggling automatic watering:', error);
      Alert.alert('Error', 'Failed to update automatic watering setting');
    } finally {
      setLoading(false);
    }
  };

  const handleIntervalToggle = async (enabled) => {
    try {
      setLoading(true);
      await setIntervalWatering(currentUser.uid, plant.id, enabled, wateringInterval);
      setIntervalEnabled(enabled);

      Alert.alert(
        'Success',
        `Interval watering ${enabled ? 'enabled' : 'disabled'} for ${plant.name}`
      );
    } catch (error) {
      console.error('Error toggling interval watering:', error);
      Alert.alert('Error', 'Failed to update interval watering setting');
    } finally {
      setLoading(false);
    }
  };

  const handleIntervalChange = () => {
    Alert.alert(
      'Set Watering Interval',
      'How often should the plant be watered automatically?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: '6 hours', onPress: () => updateInterval(6) },
        { text: '12 hours', onPress: () => updateInterval(12) },
        { text: '24 hours', onPress: () => updateInterval(24) },
        { text: '48 hours', onPress: () => updateInterval(48) },
        { text: '72 hours', onPress: () => updateInterval(72) },
        { text: 'Custom', onPress: () => showCustomIntervalDialog() },
      ]
    );
  };

  const showCustomIntervalDialog = () => {
    Alert.prompt(
      'Custom Interval',
      'Enter watering interval in hours (1-168):',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Set',
          onPress: (value) => {
            const hours = parseInt(value);
            if (hours && hours >= 1 && hours <= 168) {
              updateInterval(hours);
            } else {
              Alert.alert('Error', 'Please enter a valid number between 1 and 168 hours');
            }
          }
        },
      ],
      'plain-text',
      wateringInterval.toString()
    );
  };

  const updateInterval = async (hours) => {
    try {
      setLoading(true);
      await updateWateringInterval(currentUser.uid, plant.id, hours);
      setWateringInterval(hours);

      Alert.alert('Success', `Watering interval updated to ${hours} hours`);
    } catch (error) {
      console.error('Error updating watering interval:', error);
      Alert.alert('Error', 'Failed to update watering interval');
    } finally {
      setLoading(false);
    }
  };

  const needsWatering = plant.conditions && sensorData ?
    sensorData.soilMoisture < plant.conditions.soilMoisture.min : false;

  const intervalDue = checkIntervalWateringDue(plant);

  const getNextWateringTime = () => {
    if (!plant.lastIntervalWatering || !plant.wateringInterval) return 'Not set';

    const lastWatering = new Date(plant.lastIntervalWatering);
    const nextWatering = new Date(lastWatering.getTime() + (plant.wateringInterval * 60 * 60 * 1000));
    const now = new Date();

    if (nextWatering <= now) return 'Due now';

    const diffMs = nextWatering.getTime() - now.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (diffHours > 0) {
      return `In ${diffHours}h ${diffMinutes}m`;
    } else {
      return `In ${diffMinutes}m`;
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Watering Controls</Text>

      {/* Manual Watering */}
      <View style={styles.controlSection}>
        <View style={styles.controlHeader}>
          <Icon name="hand-water" size={SIZES.iconMedium} color={COLORS.primary} />
          <Text style={styles.controlTitle}>Manual Watering</Text>
        </View>

        <TouchableOpacity
          style={[
            styles.waterButton,
            isWatering && styles.waterButtonActive,
            loading && styles.waterButtonDisabled
          ]}
          onPress={handleManualWatering}
          disabled={loading}
        >
          <Icon
            name={isWatering ? "stop" : "water"}
            size={SIZES.iconMedium}
            color={COLORS.white}
          />
          <Text style={styles.waterButtonText}>
            {isWatering ? 'Stop Watering' : 'Water Now'}
          </Text>
        </TouchableOpacity>

        {isWatering && isManualWatering && (
          <Text style={styles.statusText}>
            💧 Currently watering manually...
          </Text>
        )}
      </View>

      {/* Condition-Based Automatic Watering */}
      <View style={styles.controlSection}>
        <View style={styles.controlHeader}>
          <Icon name="robot" size={SIZES.iconMedium} color={COLORS.secondary} />
          <Text style={styles.controlTitle}>Condition-Based Watering</Text>
        </View>

        <View style={styles.automaticControl}>
          <View style={styles.automaticInfo}>
            <Text style={styles.automaticDescription}>
              Water automatically when soil moisture drops below {plant.conditions?.soilMoisture?.min || 30}%
            </Text>
            {needsWatering && automaticEnabled && (
              <Text style={styles.needsWateringText}>
                🚨 Plant needs watering! Auto-watering will trigger soon.
              </Text>
            )}
          </View>

          <Switch
            value={automaticEnabled}
            onValueChange={handleAutomaticToggle}
            trackColor={{ false: COLORS.gray300, true: COLORS.secondaryLight }}
            thumbColor={automaticEnabled ? COLORS.secondary : COLORS.gray500}
            disabled={loading}
          />
        </View>
      </View>

      {/* Interval-Based Automatic Watering */}
      <View style={styles.controlSection}>
        <View style={styles.controlHeader}>
          <Icon name="clock-outline" size={SIZES.iconMedium} color={COLORS.accent} />
          <Text style={styles.controlTitle}>Interval Watering</Text>
        </View>

        <View style={styles.automaticControl}>
          <View style={styles.automaticInfo}>
            <Text style={styles.automaticDescription}>
              Water automatically every {wateringInterval} hours
            </Text>
            <TouchableOpacity
              style={styles.intervalEditButton}
              onPress={handleIntervalChange}
              disabled={loading}
            >
              <Icon name="pencil" size={SIZES.iconSmall} color={COLORS.accent} />
              <Text style={styles.intervalEditText}>Change interval</Text>
            </TouchableOpacity>
            {intervalEnabled && (
              <Text style={styles.nextWateringText}>
                Next watering: {getNextWateringTime()}
              </Text>
            )}
            {intervalDue && intervalEnabled && (
              <Text style={styles.needsWateringText}>
                ⏰ Interval watering is due!
              </Text>
            )}
          </View>

          <Switch
            value={intervalEnabled}
            onValueChange={handleIntervalToggle}
            trackColor={{ false: COLORS.gray300, true: COLORS.accentLight }}
            thumbColor={intervalEnabled ? COLORS.accent : COLORS.gray500}
            disabled={loading}
          />
        </View>
      </View>

      {/* Status Info */}
      <View style={styles.statusSection}>
        <Text style={styles.statusTitle}>Current Status</Text>
        <View style={styles.statusRow}>
          <Text style={styles.statusLabel}>Soil Moisture:</Text>
          <Text style={[
            styles.statusValue,
            { color: needsWatering ? COLORS.danger : COLORS.success }
          ]}>
            {sensorData?.soilMoisture || 0}%
          </Text>
        </View>
        <View style={styles.statusRow}>
          <Text style={styles.statusLabel}>Target Range:</Text>
          <Text style={styles.statusValue}>
            {plant.conditions?.soilMoisture?.min || 0}% - {plant.conditions?.soilMoisture?.max || 100}%
          </Text>
        </View>
        <View style={styles.statusRow}>
          <Text style={styles.statusLabel}>Condition-Based:</Text>
          <Text style={[
            styles.statusValue,
            { color: automaticEnabled ? COLORS.success : COLORS.gray500 }
          ]}>
            {automaticEnabled ? 'Enabled' : 'Disabled'}
          </Text>
        </View>
        <View style={styles.statusRow}>
          <Text style={styles.statusLabel}>Interval Watering:</Text>
          <Text style={[
            styles.statusValue,
            { color: intervalEnabled ? COLORS.success : COLORS.gray500 }
          ]}>
            {intervalEnabled ? `Every ${wateringInterval}h` : 'Disabled'}
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radiusLarge,
    padding: SIZES.paddingLarge,
    marginVertical: SIZES.marginMedium,
    ...SHADOWS.medium,
  },
  title: {
    fontSize: FONTS.h3,
    fontWeight: '700',
    color: COLORS.textPrimary,
    marginBottom: SIZES.marginLarge,
    textAlign: 'center',
  },
  controlSection: {
    marginBottom: SIZES.marginLarge,
  },
  controlHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SIZES.marginMedium,
  },
  controlTitle: {
    fontSize: FONTS.h4,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginLeft: SIZES.marginSmall,
  },
  waterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radiusMedium,
    paddingVertical: SIZES.paddingMedium,
    paddingHorizontal: SIZES.paddingLarge,
  },
  waterButtonActive: {
    backgroundColor: COLORS.danger,
  },
  waterButtonDisabled: {
    backgroundColor: COLORS.gray400,
  },
  waterButtonText: {
    fontSize: FONTS.body1,
    fontWeight: '600',
    color: COLORS.white,
    marginLeft: SIZES.marginSmall,
  },
  statusText: {
    fontSize: FONTS.body3,
    color: COLORS.primary,
    textAlign: 'center',
    marginTop: SIZES.marginSmall,
    fontWeight: '500',
  },
  automaticControl: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  automaticInfo: {
    flex: 1,
    marginRight: SIZES.marginMedium,
  },
  automaticDescription: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    lineHeight: 18,
  },
  needsWateringText: {
    fontSize: FONTS.body3,
    color: COLORS.danger,
    marginTop: SIZES.marginXS,
    fontWeight: '500',
  },
  statusSection: {
    borderTopWidth: 1,
    borderTopColor: COLORS.gray200,
    paddingTop: SIZES.paddingMedium,
  },
  statusTitle: {
    fontSize: FONTS.body1,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SIZES.marginMedium,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.marginSmall,
  },
  statusLabel: {
    fontSize: FONTS.body2,
    color: COLORS.textSecondary,
  },
  statusValue: {
    fontSize: FONTS.body2,
    fontWeight: '500',
    color: COLORS.textPrimary,
  },
  intervalEditButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: SIZES.marginXS,
    paddingVertical: SIZES.paddingXS,
  },
  intervalEditText: {
    fontSize: FONTS.body3,
    color: COLORS.accent,
    marginLeft: SIZES.marginXS,
    fontWeight: '500',
  },
  nextWateringText: {
    fontSize: FONTS.body3,
    color: COLORS.textSecondary,
    marginTop: SIZES.marginXS,
    fontStyle: 'italic',
  },
});

export default WateringControls;

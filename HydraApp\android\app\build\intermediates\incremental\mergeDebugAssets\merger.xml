<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":react-native-vector-icons" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\HYDRA-IOT\HydraApp\node_modules\react-native-vector-icons\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-svg" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\HYDRA-IOT\HydraApp\node_modules\react-native-svg\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-reanimated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\HYDRA-IOT\HydraApp\node_modules\react-native-reanimated\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-linear-gradient" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\HYDRA-IOT\HydraApp\node_modules\react-native-linear-gradient\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-image-picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\HYDRA-IOT\HydraApp\node_modules\react-native-image-picker\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-firebase_app" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\app\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-firebase_messaging" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\HYDRA-IOT\HydraApp\node_modules\@react-native-firebase\messaging\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\HYDRA-IOT\HydraApp\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\HYDRA-IOT\HydraApp\node_modules\react-native-screens\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\HYDRA-IOT\HydraApp\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-gesture-handler" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\HYDRA-IOT\HydraApp\node_modules\react-native-gesture-handler\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\HYDRA-IOT\HydraApp\android\app\src\main\assets"><file name="fonts/AntDesign.ttf" path="E:\HYDRA-IOT\HydraApp\android\app\src\main\assets\fonts\AntDesign.ttf"/><file name="fonts/Entypo.ttf" path="E:\HYDRA-IOT\HydraApp\android\app\src\main\assets\fonts\Entypo.ttf"/><file name="fonts/EvilIcons.ttf" path="E:\HYDRA-IOT\HydraApp\android\app\src\main\assets\fonts\EvilIcons.ttf"/><file name="fonts/Feather.ttf" path="E:\HYDRA-IOT\HydraApp\android\app\src\main\assets\fonts\Feather.ttf"/><file name="fonts/FontAwesome.ttf" path="E:\HYDRA-IOT\HydraApp\android\app\src\main\assets\fonts\FontAwesome.ttf"/><file name="fonts/FontAwesome5_Brands.ttf" path="E:\HYDRA-IOT\HydraApp\android\app\src\main\assets\fonts\FontAwesome5_Brands.ttf"/><file name="fonts/FontAwesome5_Regular.ttf" path="E:\HYDRA-IOT\HydraApp\android\app\src\main\assets\fonts\FontAwesome5_Regular.ttf"/><file name="fonts/FontAwesome5_Solid.ttf" path="E:\HYDRA-IOT\HydraApp\android\app\src\main\assets\fonts\FontAwesome5_Solid.ttf"/><file name="fonts/FontAwesome6_Brands.ttf" path="E:\HYDRA-IOT\HydraApp\android\app\src\main\assets\fonts\FontAwesome6_Brands.ttf"/><file name="fonts/FontAwesome6_Regular.ttf" path="E:\HYDRA-IOT\HydraApp\android\app\src\main\assets\fonts\FontAwesome6_Regular.ttf"/><file name="fonts/FontAwesome6_Solid.ttf" path="E:\HYDRA-IOT\HydraApp\android\app\src\main\assets\fonts\FontAwesome6_Solid.ttf"/><file name="fonts/Fontisto.ttf" path="E:\HYDRA-IOT\HydraApp\android\app\src\main\assets\fonts\Fontisto.ttf"/><file name="fonts/Foundation.ttf" path="E:\HYDRA-IOT\HydraApp\android\app\src\main\assets\fonts\Foundation.ttf"/><file name="fonts/Ionicons.ttf" path="E:\HYDRA-IOT\HydraApp\android\app\src\main\assets\fonts\Ionicons.ttf"/><file name="fonts/MaterialCommunityIcons.ttf" path="E:\HYDRA-IOT\HydraApp\android\app\src\main\assets\fonts\MaterialCommunityIcons.ttf"/><file name="fonts/MaterialIcons.ttf" path="E:\HYDRA-IOT\HydraApp\android\app\src\main\assets\fonts\MaterialIcons.ttf"/><file name="fonts/Octicons.ttf" path="E:\HYDRA-IOT\HydraApp\android\app\src\main\assets\fonts\Octicons.ttf"/><file name="fonts/SimpleLineIcons.ttf" path="E:\HYDRA-IOT\HydraApp\android\app\src\main\assets\fonts\SimpleLineIcons.ttf"/><file name="fonts/Zocial.ttf" path="E:\HYDRA-IOT\HydraApp\android\app\src\main\assets\fonts\Zocial.ttf"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\HYDRA-IOT\HydraApp\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\HYDRA-IOT\HydraApp\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>
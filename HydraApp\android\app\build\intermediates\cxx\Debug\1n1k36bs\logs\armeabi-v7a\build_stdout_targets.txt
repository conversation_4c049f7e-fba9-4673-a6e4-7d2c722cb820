ninja: Entering directory `E:\HYDRA-IOT\HydraApp\android\app\.cxx\Debug\1n1k36bs\armeabi-v7a'
[0/2] Re-checking globbed directories...
[1/2] Re-running CMake...
-- Configuring done
-- Generating done
-- Build files have been written to: E:/HYDRA-IOT/HydraApp/android/app/.cxx/Debug/1n1k36bs/armeabi-v7a
[0/2] Re-checking globbed directories...
[1/3] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[2/3] Building CXX object CMakeFiles/appmodules.dir/E_/HYDRA-IOT/HydraApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[3/3] Linking CXX shared library E:\HYDRA-IOT\HydraApp\android\app\build\intermediates\cxx\Debug\1n1k36bs\obj\armeabi-v7a\libappmodules.so

import React, { useState, useEffect } from 'react';
import {
  View,
  Image,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Platform,
  PermissionsAndroid,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, SIZES } from '../constants/theme';
import { launchImageLibrary, launchCamera } from 'react-native-image-picker';
import { updatePlantSettings, getPlantSettings } from '../services/databaseService';
import { useAuth } from '../context/AuthContext';
import PlantIcon from './PlantIcon';

// We'll use a component-based placeholder instead of an image file

const PlantImage = ({
  plantId,
  imageUrl,
  imageBase64,
  plantType = 'default',
  size = 'medium',
  editable = false,
  onImageChange,
  style
}) => {
  const { currentUser } = useAuth();
  // If we have a base64 image, use it with the data URL format
  const initialImage = imageBase64
    ? `data:image/jpeg;base64,${imageBase64}`
    : imageUrl;
  const [image, setImage] = useState(initialImage);
  const [loading, setLoading] = useState(false);

  // Load image from Firestore when component mounts
  useEffect(() => {
    const loadPlantImage = async () => {
      if (!currentUser || !plantId) return;

      try {
        const settings = await getPlantSettings(currentUser.uid, plantId);
        if (settings && settings.imageBase64) {
          // Set image with data URL format
          setImage(`data:image/jpeg;base64,${settings.imageBase64}`);
        }
      } catch (error) {
        console.error('Error loading plant image:', error);
      }
    };

    loadPlantImage();
  }, [currentUser, plantId]);

  // Get image size based on size prop
  const getImageSize = () => {
    switch (size) {
      case 'small':
        return { width: 60, height: 60 };
      case 'medium':
        return { width: 100, height: 100 };
      case 'large':
        return { width: 200, height: 200 };
      default:
        return { width: 100, height: 100 };
    }
  };

  // Request camera permission (Android only)
  const requestCameraPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: 'Camera Permission',
            message: 'Hydra needs access to your camera to take plant photos',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn(err);
        return false;
      }
    }
    return true;
  };

  // Handle image selection
  const selectImage = () => {
    if (!editable) return;

    Alert.alert(
      'Change Plant Image',
      'Choose an option',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Take Photo',
          onPress: takePhoto,
        },
        {
          text: 'Choose from Gallery',
          onPress: chooseFromGallery,
        },
      ],
    );
  };

  // Take photo with camera
  const takePhoto = async () => {
    const hasPermission = await requestCameraPermission();

    if (!hasPermission) {
      Alert.alert('Permission Denied', 'Camera permission is required to take photos');
      return;
    }

    const options = {
      mediaType: 'photo',
      quality: 0.8,
    };

    try {
      const result = await launchCamera(options);

      if (result.didCancel) {
        return;
      }

      if (result.errorCode) {
        Alert.alert('Error', result.errorMessage);
        return;
      }

      if (result.assets && result.assets.length > 0) {
        uploadImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo');
    }
  };

  // Choose image from gallery
  const chooseFromGallery = async () => {
    const options = {
      mediaType: 'photo',
      quality: 0.8,
    };

    try {
      const result = await launchImageLibrary(options);

      if (result.didCancel) {
        return;
      }

      if (result.errorCode) {
        Alert.alert('Error', result.errorMessage);
        return;
      }

      if (result.assets && result.assets.length > 0) {
        uploadImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error selecting image:', error);
      Alert.alert('Error', 'Failed to select image');
    }
  };

  // Convert image to base64 and store in Firestore
  const uploadImage = async (uri) => {
    if (!currentUser) {
      Alert.alert('Error', 'You must be logged in to upload images');
      return;
    }

    setLoading(true);

    try {
      // Convert image URI to base64
      const response = await fetch(uri);
      const blob = await response.blob();

      // Convert blob to base64
      const reader = new FileReader();

      // Create a promise to handle the FileReader async operation
      const base64Promise = new Promise((resolve, reject) => {
        reader.onload = () => {
          // Get base64 string (remove the data:image/jpeg;base64, prefix)
          const base64String = reader.result.split(',')[1];
          resolve(base64String);
        };
        reader.onerror = (error) => {
          reject(error);
        };
      });

      // Read the blob as a data URL (base64)
      reader.readAsDataURL(blob);

      // Wait for the base64 conversion to complete
      const base64Image = await base64Promise;

      // Update plant settings with base64 image
      await updatePlantSettings(currentUser.uid, plantId, {
        imageBase64: base64Image,
      });

      // Update local state with the data URL for display
      const dataUrl = `data:image/jpeg;base64,${base64Image}`;
      setImage(dataUrl);

      // Call onImageChange callback if provided
      if (onImageChange) {
        onImageChange(dataUrl);
      }

      Alert.alert('Success', 'Plant image updated successfully');
    } catch (error) {
      console.error('Error uploading image:', error);
      Alert.alert('Error', 'Failed to upload image');
    } finally {
      setLoading(false);
    }
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={selectImage}
      disabled={!editable}
    >
      {image ? (
        <Image
          source={{ uri: image }}
          style={[styles.image, getImageSize()]}
          resizeMode="cover"
        />
      ) : (
        <View style={[styles.placeholderContainer, getImageSize()]}>
          <PlantIcon
            plantType={plantType}
            width={getImageSize().width}
            height={getImageSize().height}
            style={styles.plantIcon}
          />
        </View>
      )}

      {editable && (
        <View style={styles.editButton}>
          <Icon name="camera" size={16} color={COLORS.white} />
        </View>
      )}

      {loading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={COLORS.primary} />
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  image: {
    borderRadius: SIZES.radiusMedium,
  },
  placeholderContainer: {
    borderRadius: SIZES.radiusLarge,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  plantIcon: {
    // PlantIcon will handle its own styling
  },
  editButton: {
    position: 'absolute',
    bottom: 5,
    right: 5,
    backgroundColor: COLORS.primary,
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: COLORS.white,
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: SIZES.radiusMedium,
  },
});

export default PlantImage;

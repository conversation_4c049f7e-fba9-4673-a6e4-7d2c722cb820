[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: x86_64", "file_": "E:\\HYDRA-IOT\\HydraApp\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'E:\\HYDRA-IOT\\HydraApp\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\3u1m4i71\\x86_64\\android_gradle_build.json' was up-to-date", "file_": "E:\\HYDRA-IOT\\HydraApp\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "E:\\HYDRA-IOT\\HydraApp\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]
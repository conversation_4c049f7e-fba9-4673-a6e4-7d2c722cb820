import React from 'react';
import { View, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import { COLORS, SIZES } from '../constants/theme';

const PlantIcon = ({
  plantType = 'default',
  size = 'medium',
  width,
  height,
  style
}) => {
  // Get icon name based on plant type
  const getIconName = () => {
    switch (plantType.toLowerCase()) {
      case 'tomato':
        return 'fruit-cherries';
      case 'basil':
        return 'leaf';
      case 'succulent':
        return 'cactus';
      case 'fern':
        return 'tree';
      case 'orchid':
        return 'flower';
      case 'user':
        return 'account';
      default:
        return 'leaf';
    }
  };

  // Get gradient colors based on plant type
  const getGradientColors = () => {
    switch (plantType.toLowerCase()) {
      case 'tomato':
        return ['#FF6B6B', '#FF8E53'];
      case 'basil':
        return ['#4ECDC4', '#44A08D'];
      case 'succulent':
        return ['#A8E6CF', '#7FCDCD'];
      case 'fern':
        return ['#81C784', '#66BB6A'];
      case 'orchid':
        return ['#CE93D8', '#BA68C8'];
      case 'user':
        return ['#6C63FF', '#9C88FF'];
      default:
        return COLORS.gradientPrimary;
    }
  };

  // Get size dimensions
  const getSizeDimensions = () => {
    // If width and height are provided, use them
    if (width && height) {
      return {
        width,
        height,
        iconSize: Math.min(width, height) * 0.4 // 40% of the smaller dimension
      };
    }

    // Otherwise use size prop
    switch (size) {
      case 'small':
        return { width: 60, height: 60, iconSize: 24 };
      case 'medium':
        return { width: 80, height: 80, iconSize: 32 };
      case 'large':
        return { width: 120, height: 120, iconSize: 48 };
      default:
        return { width: 80, height: 80, iconSize: 32 };
    }
  };

  const dimensions = getSizeDimensions();
  const gradientColors = getGradientColors();
  const iconName = getIconName();

  return (
    <LinearGradient
      colors={gradientColors}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={[
        styles.gradient,
        {
          width: dimensions.width,
          height: dimensions.height,
          borderRadius: dimensions.width / 2, // Perfect circle
        },
        style
      ]}
    >
      <View style={styles.iconContainer}>
        <Icon
          name={iconName}
          size={dimensions.iconSize}
          color={COLORS.white}
        />
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  gradient: {
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default PlantIcon;

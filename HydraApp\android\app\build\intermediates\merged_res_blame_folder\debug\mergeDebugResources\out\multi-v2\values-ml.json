{"logs": [{"outputFile": "com.hydraiot.app-mergeDebugResources-51:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79f6ab95733be72bb6af37d95e29f537\\transformed\\browser-1.4.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,267,378", "endColumns": "108,102,110,103", "endOffsets": "159,262,373,477"}, "to": {"startLines": "66,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "6885,7158,7261,7372", "endColumns": "108,102,110,103", "endOffsets": "6989,7256,7367,7471"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af63e50a53e992ea9c751fc2deb2fc57\\transformed\\appcompat-1.7.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,2932"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,131", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "276,383,489,600,691,796,918,996,1071,1162,1255,1356,1450,1550,1644,1739,1838,1929,2020,2102,2211,2315,2414,2526,2638,2759,2924,12275", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "378,484,595,686,791,913,991,1066,1157,1250,1351,1445,1545,1639,1734,1833,1924,2015,2097,2206,2310,2409,2521,2633,2754,2919,3020,12353"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0a1c3fd62b5fb636be47a17086a4f32c\\transformed\\play-services-basement-18.5.0\\res\\values-ml\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5582", "endColumns": "159", "endOffsets": "5737"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3135f157d17fbe3966fe84b667792db9\\transformed\\core-1.13.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "37,38,39,40,41,42,43,140", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3411,3513,3616,3718,3822,3925,4026,13007", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "3508,3611,3713,3817,3920,4021,4143,13103"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7312d9ce0e64b42e563cee99ce0c7cbc\\transformed\\play-services-base-18.5.0\\res\\values-ml\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,308,483,618,735,895,1016,1117,1219,1397,1509,1679,1811,1956,2113,2173,2238", "endColumns": "114,174,134,116,159,120,100,101,177,111,169,131,144,156,59,64,87", "endOffsets": "307,482,617,734,894,1015,1116,1218,1396,1508,1678,1810,1955,2112,2172,2237,2325"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4524,4643,4822,4961,5082,5246,5371,5476,5742,5924,6040,6214,6350,6499,6660,6724,6793", "endColumns": "118,178,138,120,163,124,104,105,181,115,173,135,148,160,63,68,91", "endOffsets": "4638,4817,4956,5077,5241,5366,5471,5577,5919,6035,6209,6345,6494,6655,6719,6788,6880"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd1664c1c7ce01ff711fc15460fc5485\\transformed\\credentials-1.2.0-rc01\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,113", "endOffsets": "163,277"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3097,3210", "endColumns": "112,113", "endOffsets": "3205,3319"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa0e231f4d27dc8a031f0119595b6f1\\transformed\\material-1.6.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,226,313,407,517,609,674,773,839,899,1001,1077,1135,1213,1278,1332,1449,1513,1577,1631,1711,1845,1931,2020,2126,2211,2299,2394,2461,2527,2606,2688,2779,2855,2932,3009,3080,3187,3267,3364,3464,3538,3619,3724,3782,3849,3940,4032,4094,4158,4221,4324,4440,4545,4661", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,86,93,109,91,64,98,65,59,101,75,57,77,64,53,116,63,63,53,79,133,85,88,105,84,87,94,66,65,78,81,90,75,76,76,70,106,79,96,99,73,80,104,57,66,90,91,61,63,62,102,115,104,115,83", "endOffsets": "221,308,402,512,604,669,768,834,894,996,1072,1130,1208,1273,1327,1444,1508,1572,1626,1706,1840,1926,2015,2121,2206,2294,2389,2456,2522,2601,2683,2774,2850,2927,3004,3075,3182,3262,3359,3459,3533,3614,3719,3777,3844,3935,4027,4089,4153,4216,4319,4435,4540,4656,4740"}, "to": {"startLines": "2,36,44,45,46,67,68,73,76,78,79,80,81,82,83,84,85,86,87,88,89,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3324,4148,4242,4352,6994,7059,7551,7770,7899,8001,8077,8135,8213,8278,8332,8449,8513,8577,8631,8711,9061,9147,9236,9342,9427,9515,9610,9677,9743,9822,9904,9995,10071,10148,10225,10296,10403,10483,10580,10680,10754,10835,10940,10998,11065,11156,11248,11310,11374,11437,11540,11656,11761,11877", "endLines": "5,36,44,45,46,67,68,73,76,78,79,80,81,82,83,84,85,86,87,88,89,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126", "endColumns": "12,86,93,109,91,64,98,65,59,101,75,57,77,64,53,116,63,63,53,79,133,85,88,105,84,87,94,66,65,78,81,90,75,76,76,70,106,79,96,99,73,80,104,57,66,90,91,61,63,62,102,115,104,115,83", "endOffsets": "271,3406,4237,4347,4439,7054,7153,7612,7825,7996,8072,8130,8208,8273,8327,8444,8508,8572,8626,8706,8840,9142,9231,9337,9422,9510,9605,9672,9738,9817,9899,9990,10066,10143,10220,10291,10398,10478,10575,10675,10749,10830,10935,10993,11060,11151,11243,11305,11369,11432,11535,11651,11756,11872,11956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ff5b0ce559ae890269420b3dec0e4060\\transformed\\react-android-0.79.2-debug\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,207,282,352,435,504,571,645,720,801,885,954,1034,1116,1196,1278,1364,1442,1515,1587,1683,1756,1836,1904", "endColumns": "71,79,74,69,82,68,66,73,74,80,83,68,79,81,79,81,85,77,72,71,95,72,79,67,72", "endOffsets": "122,202,277,347,430,499,566,640,715,796,880,949,1029,1111,1191,1273,1359,1437,1510,1582,1678,1751,1831,1899,1972"}, "to": {"startLines": "33,47,72,74,75,77,90,91,92,127,128,129,130,132,133,134,135,136,137,138,139,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3025,4444,7476,7617,7687,7830,8845,8912,8986,11961,12042,12126,12195,12358,12440,12520,12602,12688,12766,12839,12911,13108,13181,13261,13329", "endColumns": "71,79,74,69,82,68,66,73,74,80,83,68,79,81,79,81,85,77,72,71,95,72,79,67,72", "endOffsets": "3092,4519,7546,7682,7765,7894,8907,8981,9056,12037,12121,12190,12270,12435,12515,12597,12683,12761,12834,12906,13002,13176,13256,13324,13397"}}]}]}